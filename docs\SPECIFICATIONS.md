# FocusTime - Collaborative Focus Timer Specification

This document outlines the specifications for **FocusTime**, a real-time collaborative Pomodoro-like timer application. The app allows multiple users to join shared focus sessions with synchronized timers, custom segments, and real-time collaboration features.

## Project Overview

**FocusTime** is a web-based collaborative focus timer that enables multiple users to share synchronized timer sessions. Built with vanilla JavaScript and WebSockets, it provides real-time synchronization across devices while maintaining simplicity and ease of use.

## Core Features

### Timer Functionality

- **Collaborative Timer**: Real-time synchronized countdown display across all connected clients
- **Timer Controls**: Start, pause, stop, and next segment functionality
- **Multi-segment Support**: Unlimited custom work and break segments with individual configurations
- **Repeat Mode**: Automatic cycling through all segments when enabled
- **Precise Timing**: Client-side timing calculations with server synchronization

### Session Management

- **URL-based Sessions**: Unique session IDs in URL paths for easy sharing (e.g., `/abc-123`)
- **Session Creation**: Automatic session creation on first visit
- **Session Joining**: Join existing sessions via shared URLs
- **Session Persistence**: LocalStorage backup for offline resilience and reconnection
- **Session Export/Import**: JSON configuration export and import

### Real-time Collaboration

- **WebSocket Communication**: Real-time synchronization of timer state and user actions
- **User Presence**: Live user count and connection status
- **User Profiles**: Gravatar integration with display names and online indicators
- **Automatic Reconnection**: Resilient connection handling with heartbeat and retry logic

### Customization

- **Custom Segments**: Define unlimited segments with names, durations, and alert sounds
- **Segment Styling**: Per-segment custom CSS for personalized visual experiences
- **Audio Alerts**: Synthesized alert sounds with volume control
- **Focus Mode**: Distraction-free interface option
- **Wake Lock**: Keep screen active during timer sessions

## Technology Stack

### Frontend

- **Core**: Vanilla JavaScript (ES6+), HTML5, CSS3
- **Architecture**: Component-based modular design with event coordination
- **Storage**: LocalStorage for client-side persistence
- **Audio**: Web Audio API for synthesized alert sounds
- **External Libraries**:
  - NoSleep.js for wake lock functionality
  - QR-Creator for session sharing QR codes

### Backend

- **Runtime**: Node.js 16.0.0+
- **Framework**: Express.js for HTTP server
- **Real-time**: WebSocket server using 'ws' library
- **Session Management**: In-memory storage with automatic cleanup
- **Validation**: Message type validation with data formatting/sanitization
- **Timer Logic**: Shared timer core between client and server
- **Dependencies**:
  - dotenv for environment configuration
  - express for HTTP server
  - ws for WebSocket communication

### Development & Testing

- **Testing**: Jest framework with JSDOM for DOM testing
- **Code Quality**: Prettier for code formatting
- **Development**: Nodemon for auto-restart during development
- **Docker**: Full containerization support

## System Architecture

```
┌─────────────────┐     WebSocket     ┌─────────────────┐
│   Client App    │ <-------------->  │   Server App    │
│                 │                   │                 │
│ ┌─────────────┐ │                   │ ┌─────────────┐ │
│ │ Coordinator │ │                   │ │   Session   │ │
│ │             │ │                   │ │   Manager   │ │
│ └─────────────┘ │                   │ └─────────────┘ │
│                 │                   │                 │
│ ┌─────────────┐ │                   │ ┌─────────────┐ │
│ │ Components  │ │                   │ │ Formatters/ │ │
│ │ - Timer     │ │                   │ │ Validators  │ │
│ │ - Sessions  │ │                   │ │             │ │
│ │ - Settings  │ │                   │ │             │ │
│ │ - User      │ │                   │ │             │ │
│ │ - Share     │ │                   │ │             │ │
│ │ - Alerts    │ │                   │ │             │ │
│ └─────────────┘ │                   │ └─────────────┘ │
│                 │                   │                 │
│ ┌─────────────┐ │                   │ ┌─────────────┐ │
│ │ localStorage│ │                   │ │ TimerCore   │ │
│ │             │ │                   │ │ (Shared)    │ │
│ └─────────────┘ │                   │ └─────────────┘ │
└─────────────────┘                   └─────────────────┘
```

### Client Architecture

- **Component System**: Modular architecture with independent components
- **Coordinator**: Central event dispatcher that coordinates component updates
- **WebSocket Client**: Manages real-time server communication
- **localStorage**: Client-side persistence and offline resilience
- **Shared Logic**: TimerCore shared between client and server for consistency

### Server Architecture

- **Express Server**: HTTP server for static file serving and API endpoints
- **WebSocket Server**: Real-time communication hub
- **Session Manager**: In-memory session state management
- **Message Processing**: Comprehensive validation and formatting
- **Timer Logic**: Server-side timer calculations using shared TimerCore

## Component Structure

### Frontend Components

#### Timer Component (`timer.js`)

- Timer display and controls
- Local timer calculations and synchronization
- Segment transitions and state management
- Integration with TimerCore for precise calculations

#### Sessions Component (`sessions.js`)

- Session creation and joining
- Session list management
- URL handling and navigation
- Connected users display

#### Settings Component (`settings.js`)

- Segment configuration interface
- Session metadata editing
- Audio/visual preference controls
- Export/import functionality

#### User Component (`user.js`)

- User profile management
- Gravatar integration
- Profile modal interface

#### Share Component (`share.js`)

- Session URL sharing
- QR code generation
- Clipboard integration

#### Alerts Component (`alerts.js`)

- Audio system management
- Sound synthesis and playback
- Volume control
- AudioContext unlock handling

#### WebSocket Client (`websocket.js`)

- Server connection management
- Message sending and receiving
- Automatic reconnection logic
- Heartbeat monitoring

#### Coordinator (`coordinator.js`)

- Central event orchestration
- Component registration and communication
- Data synchronization pipeline
- Event prioritization and sequencing

### Backend Components

#### Session Manager (`sessions.js`)

- Session lifecycle management
- User connection tracking
- Message routing and broadcasting
- Cleanup and garbage collection

#### Formatters (`formatters.js`)

- Message formatting and sanitization
- Data transformation utilities
- Response generation
- Input validation helpers

#### Validators (`validators.js`)

- Message schema validation
- Data integrity checking
- Error handling and reporting

#### Timer Server (`timer.js`)

- Extended TimerCore for server use
- Server-specific timer functionality

## Message Protocol

### WebSocket Message Types

#### Client → Server Messages

| Message Type          | Purpose                      | Required Fields     |
| --------------------- | ---------------------------- | ------------------- |
| `join_session`        | Join/create session          | `sessionId`, `user` |
| `segments_update`     | Update segment configuration | `session`           |
| `timer_start`         | Start timer                  | -                   |
| `timer_pause`         | Pause timer                  | -                   |
| `timer_stop`          | Stop timer                   | -                   |
| `timer_repeat`        | Toggle repeat mode           | `repeat`            |
| `timer_next_segment`  | Move to next segment         | -                   |
| `timer_update`        | Sync timer state             | `timer`             |
| `user_update`         | Update user profile          | `user`              |
| `get_connected_users` | Request user list            | -                   |
| `ping`                | Connection heartbeat         | -                   |

#### Server → Client Messages

| Message Type        | Purpose                 | Contains                           |
| ------------------- | ----------------------- | ---------------------------------- |
| `session_created`   | New session created     | `sessionId`, `clientId`            |
| `session_joined`    | Joined existing session | `sessionId`, `clientId`, `session` |
| `segments_updated`  | Segments changed        | `sessionId`, `session`             |
| `timer_updated`     | Timer state changed     | `sessionId`, `timer`               |
| `user_connected`    | User joined session     | `sessionId`, `user`                |
| `user_disconnected` | User left session       | `sessionId`, `user`                |
| `user_updated`      | User profile changed    | `sessionId`, `user`                |
| `connected_users`   | User list response      | `sessionId`, `users`               |
| `error`             | Error occurred          | `message`                          |
| `pong`              | Heartbeat response      | -                                  |

## Data Models

### Session Data Structure

The application uses a sophisticated data management system with both client-side and server-side representations.

#### Client Session Data (localStorage)

Sessions are stored in localStorage using the key `session_data` with individual session objects:

```javascript
{
  "session-id": {
    id: "session-id",
    sessionId: "session-id", // Duplicate for compatibility
    name: "Focus and Break Session",
    description: "Focus and break timer",
    segments: {
      lastUpdated: 1703097600000, // Timestamp
      items: [
        {
          name: "Focus",
          duration: 1500, // Duration in seconds
          alert: "Gentle",
          customCSS: "/* Custom CSS for this segment */"
        },
        {
          name: "Short Break",
          duration: 300,
          alert: "Default",
          customCSS: ""
        }
      ]
    },
    timer: {
      repeat: false,
      currentSegment: 0,
      timeRemaining: 1500000, // Time in milliseconds
      isRunning: false,
      isPaused: false,
      startedAt: 0, // Client-calculated timestamp
      startedSegment: 0,
      pausedAt: 0, // Client-calculated timestamp
      timePaused: 0 // Total paused time in milliseconds
    },
    users: [], // Array of connected users
    user: {
      clientId: "uuid-v4",
      name: "User Name",
      email: "<EMAIL>",
      avatarUrl: "gravatar-url"
    }
  }
}
```

#### Server Session Data (Memory)

Server maintains extended session objects with additional metadata:

```javascript
{
  sessionId: "session-id",
  createdAt: 1703097600000,
  lastActivity: 1703097600000,
  emptyAt: null, // Timestamp when session became empty
  name: "Session Name",
  description: "Session Description",
  segments: {
    lastUpdated: 1703097600000,
    items: [/* segment objects */]
  },
  timer: {
    // Extended timer state with server timestamps
    repeat: false,
    currentSegment: 0,
    timeRemaining: 1500000,
    isRunning: false,
    isPaused: false,
    startedAt: 1703097600000, // Server timestamp
    startedSegment: 0,
    pausedAt: 0, // Server timestamp
    timePaused: 0
  },
  timerInstance: /* TimerCore instance */,
  users: Map, // Map of clientId -> user objects
}
```

### User Data Structure

#### Client User Profile

```javascript
{
  clientId: "uuid-v4",
  name: "Display Name",
  email: "<EMAIL>",
  avatarUrl: "gravatar-url"
}
```

#### Server User Data

```javascript
{
  clientId: "hashed-client-id",
  name: "Display Name",
  avatarUrl: "gravatar-url",
  isOnline: true,
  lastPing: 1703097600000,
  offlineAt: 0, // 0 if online, timestamp if offline
  ws: /* WebSocket connection */
}
```

### Timer State Management

The timer system uses a shared core (`TimerCore`) between client and server for consistency:

#### Timer State Properties

- `repeat`: Boolean indicating repeat mode
- `currentSegment`: Zero-based index of active segment
- `timeRemaining`: Milliseconds remaining in current segment
- `isRunning`: Timer is actively counting down
- `isPaused`: Timer is paused but not stopped
- `startedAt`: Timestamp when current segment began
- `startedSegment`: Segment index when timer started
- `pausedAt`: Timestamp when timer was paused
- `timePaused`: Cumulative pause time in milliseconds

#### Timer Calculations

- Client and server maintain separate timestamps for network resilience
- Time calculations handle clock drift and network latency
- Synchronization occurs through state comparison, not timestamp sharing

## Storage and Persistence

### localStorage Schema

The application uses only two localStorage keys:

1. **`session_data`**: Complete session storage
2. **`client_config`**: User preferences and settings

### Session Import/Export

Sessions can be exported/imported as JSON configurations:

```javascript
{
  name: "Session Name",
  description: "Session Description",
  segments: [
    {
      name: "Focus",
      duration: 1500,
      alert: "Gentle",
      customCSS: "/* CSS content */"
    }
  ]
}
```

## Validation and Security

### Message Validation

All WebSocket messages undergo basic validation and formatting:

- **Type Validation**: Ensures message type exists and is recognized
- **Format Validation**: Validates IDs and basic structure through formatters
- **Data Sanitization**: All user inputs are sanitized through formatting functions
- **Error Handling**: Graceful handling of invalid messages with client feedback

The validation system uses a two-stage approach:

1. **Validators**: Check message type validity and basic structure
2. **Formatters**: Transform and sanitize message data, applying defaults where needed

### Security Measures

- **Input Sanitization**: All user inputs are sanitized and validated
- **CSS Sanitization**: Custom CSS input requires careful handling
- **Session ID Format**: Restricted to alphanumeric characters and hyphens
- **Rate Limiting**: Prevents message flooding and abuse
- **Connection Limits**: Manages concurrent connections per session

### Data Constraints

- Session IDs: 3-64 characters, alphanumeric + hyphens
- Client IDs: UUID v4 format
- String lengths: Names (50 chars), Descriptions (1000 chars), URLs (500 chars)
- Timer durations: 1 second to 24 hours
- Custom CSS: Requires sanitization for XSS prevention

## User Interface Specification

### Main Interface Components

#### Timer Display

- **Primary Timer**: Large, prominent countdown display (MM:SS format)
- **Segment Info**: Current segment name and progress indicator (e.g., "1/3")
- **Control Buttons**: Integrated timer controls within display area
  - Audio toggle with volume indicators
  - Segment status/progress
  - Repeat mode toggle with visual state
  - Wake lock toggle
  - Focus mode toggle

#### Control Panel

- **Primary Controls**: Large, accessible control buttons
  - Start/Play button (triangle icon)
  - Pause button (double bar icon, shown when running)
  - Stop button (square icon)
  - Next segment button (skip forward icon)

#### Session Header

- **Session Name**: Editable session title
- **Session Description**: Brief session description
- **Connected Users**: Live count of connected participants

#### Corner Controls

- **Share Button** (top-left): Session URL sharing with QR code
- **Settings Button** (top-right): Access to configuration
- **User Profile** (bottom-right): Profile management with Gravatar
- **Sessions List** (bottom-left): Session navigation and management

### Modal Interfaces

#### Settings Modal

- **Session Configuration**:
  - Session name and description editing
  - Audio alerts toggle
  - Repeat mode toggle
  - Wake lock toggle
  - Focus mode toggle

#### Segments Modal

- **Segment Management**:
  - Collapsible segment list with expand/collapse controls
  - Inline editing for segment properties:
    - Segment name
    - Duration (in minutes, max 1440)
    - Alert sound selection dropdown
    - Custom CSS textarea
  - Add new segment button
  - Delete segment with confirmation
  - Export/import configuration

#### User Profile Modal

- **Profile Management**:
  - Name input field
  - Email input field (for Gravatar)
  - Avatar preview
  - Save/cancel actions

#### Share Modal

- **Session Sharing**:
  - Session URL display
  - QR code generation
  - Copy to clipboard functionality
  - Manual copy fallback

### Responsive Design

#### Desktop Layout

- Centered container with optimal timer visibility
- Side-by-side control layouts
- Modal overlays with backdrop

#### Mobile Layout

- Full-width timer display
- Stacked control layouts
- Touch-optimized button sizes
- Swipe gestures for segment navigation

### Visual States

#### Timer States

- **Body Classes**: Applied to document body for global styling
  - `timer-stopped`: Default state
  - `timer-running`: Active countdown
  - `timer-paused`: Paused state
  - `timer-repeat`: Repeat mode enabled

#### Button States

- **Audio Button**: Volume bar visibility based on level
- **Repeat Button**: Strike-through when disabled
- **Wake Lock**: Lock icon changes based on status
- **Focus Mode**: Eye icon changes when active

#### User Status Indicators

- **Connection Status**: Color-coded borders on user avatars
  - Green: Online
  - Red: Offline
  - Yellow: Connecting

### Accessibility Features

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: ARIA labels and descriptions
- **High Contrast**: Clear visual hierarchy
- **Focus Indicators**: Visible focus states
- **Audio Feedback**: Optional audio cues for state changes

### Custom Styling System

#### Segment CSS Application

- Custom CSS applied dynamically per segment
- Scoped to prevent interference with core UI
- Real-time preview during editing
- Safety validation to prevent UI breaking

#### Theme Support

- CSS custom properties for consistent theming
- Segment-specific color schemes
- Background and accent color customization

## Message Protocol Details

### Client to Server Messages

#### join_session

```javascript
{
  type: "join_session",
  sessionId: "abc-123-def",
  user: {
    clientId: "550e8400-e29b-41d4-a716-446655440000",
    name: "John Doe",
    avatarUrl: "https://gravatar.com/avatar/..."
  }
}
```

#### segments_update

```javascript
{
  type: "segments_update",
  session: {
    name: "Focus Session",
    description: "25-5-15 Pomodoro",
    segments: {
      lastUpdated: 1703097600000,
      items: [
        {
          name: "Focus",
          duration: 1500,
          alert: "Gentle",
          customCSS: "/* Custom styles */"
        }
      ]
    }
  }
}
```

#### Timer Control Messages

```javascript
// Start timer
{ type: "timer_start" }

// Pause timer
{ type: "timer_pause" }

// Stop timer
{ type: "timer_stop" }

// Toggle repeat mode
{
  type: "timer_repeat",
  repeat: true
}

// Next segment
{ type: "timer_next_segment" }
```

#### timer_update (from localStorage sync)

```javascript
{
  type: "timer_update",
  timer: {
    repeat: false,
    currentSegment: 0,
    timeRemaining: 1200000,
    isRunning: true,
    isPaused: false
  }
}
```

#### user_update

```javascript
{
  type: "user_update",
  user: {
    name: "New Name",
    avatarUrl: "https://gravatar.com/avatar/new-hash"
  }
}
```

#### Utility Messages

```javascript
// Get connected users
{
  type: 'get_connected_users';
}

// Heartbeat
{
  type: 'ping';
}
```

### Server to Client Messages

#### session_created

```javascript
{
  type: "session_created",
  sessionId: "abc-123-def",
  clientId: "hashed-client-id"
}
```

#### session_joined

```javascript
{
  type: "session_joined",
  sessionId: "abc-123-def",
  clientId: "hashed-client-id",
  session: {
    name: "Focus Session",
    description: "25-5-15 Pomodoro",
    segments: {
      lastUpdated: 1703097600000,
      items: [/* segment objects */]
    },
    timer: {
      repeat: false,
      currentSegment: 0,
      timeRemaining: 1500000,
      isRunning: false,
      isPaused: false
    },
    users: [
      {
        clientId: "hashed-id",
        name: "John Doe",
        avatarUrl: "https://gravatar.com/avatar/...",
        isOnline: true
      }
    ]
  }
}
```

#### segments_updated

```javascript
{
  type: "segments_updated",
  sessionId: "abc-123-def",
  session: {
    name: "Updated Session",
    description: "Updated description",
    segments: {
      lastUpdated: 1703097700000,
      items: [/* updated segments */]
    }
  }
}
```

#### timer_updated

```javascript
{
  type: "timer_updated",
  sessionId: "abc-123-def",
  timer: {
    repeat: false,
    currentSegment: 1,
    timeRemaining: 300000,
    isRunning: true,
    isPaused: false
  }
}
```

#### User Event Messages

```javascript
// User connected
{
  type: "user_connected",
  sessionId: "abc-123-def",
  user: {
    clientId: "hashed-id",
    name: "Jane Doe",
    avatarUrl: "https://gravatar.com/avatar/...",
    isOnline: true
  }
}

// User disconnected
{
  type: "user_disconnected",
  sessionId: "abc-123-def",
  user: {
    clientId: "hashed-id",
    name: "Jane Doe",
    avatarUrl: "https://gravatar.com/avatar/...",
    isOnline: false
  }
}

// User updated
{
  type: "user_updated",
  sessionId: "abc-123-def",
  user: {
    clientId: "hashed-id",
    name: "Jane Smith",
    avatarUrl: "https://gravatar.com/avatar/new-hash",
    isOnline: true
  }
}

// Connected users list
{
  type: "connected_users",
  sessionId: "abc-123-def",
  users: [
    {
      clientId: "hashed-id-1",
      name: "John Doe",
      avatarUrl: "https://gravatar.com/avatar/...",
      isOnline: true
    },
    {
      clientId: "hashed-id-2",
      name: "Jane Smith",
      avatarUrl: "https://gravatar.com/avatar/...",
      isOnline: true
    }
  ]
}
```

#### Error and Utility Messages

```javascript
// Error message
{
  type: "error",
  message: "Session not found"
}

// Pong response
{ type: "pong" }
```

## Project Structure

```
focustime/
├── docs/
│   ├── SPECIFICATIONS.md       # This document
│   └── DATA_STRUCTURES.md      # Complete data structure documentation
├── public/
│   ├── css/
│   │   ├── styles.css          # Main stylesheet
│   │   └── components/         # Component-specific styles
│   │       ├── base.css
│   │       ├── buttons.css
│   │       ├── confirm-dialog.css
│   │       ├── controls.css
│   │       ├── modal.css
│   │       ├── notifications.css
│   │       ├── popup.css
│   │       ├── responsive.css
│   │       ├── segments.css
│   │       ├── session.css
│   │       ├── sessions-popup.css
│   │       ├── settings.css
│   │       ├── share.css
│   │       ├── status.css
│   │       ├── timer.css
│   │       ├── user.css
│   │       └── users-popup.css
│   ├── js/
│   │   ├── alerts.js           # Audio system and alert management
│   │   ├── app.js              # Main application orchestrator
│   │   ├── coordinator.js      # Event coordination system
│   │   ├── sessions.js         # Session management and navigation
│   │   ├── settings.js         # Settings and segments configuration
│   │   ├── share.js            # Session sharing functionality
│   │   ├── timer.js            # Timer display and controls
│   │   ├── user.js             # User profile management
│   │   ├── utils.js            # Utility functions and storage
│   │   ├── websocket.js        # WebSocket client communication
│   │   └── libs/               # Third-party libraries
│   │       ├── nosleep/        # Wake lock functionality
│   │       │   └── v0.12.0/
│   │       └── qr-creator/     # QR code generation
│   │           └── v0.14.0/
│   ├── admin.html              # Admin interface (if implemented)
│   ├── index.html              # Main application interface
│   └── favicon.ico             # Application icon
├── server/
│   ├── .env                    # Environment configuration
│   ├── .env.example            # Environment template
│   ├── constants.js            # Server constants and constraints
│   ├── formatters.js           # Message and data formatting
│   ├── server.js               # Main Express + WebSocket server
│   ├── sessions.js             # Session management and coordination
│   ├── timer.js                # Server-side timer logic
│   └── validators.js           # Input validation and security
├── shared/
│   ├── constants.js            # Shared constants between client/server
│   └── timer-core.js           # Shared timer calculation logic
├── tests/
│   ├── setup.js                # Test configuration
│   └── server/
│       └── timer.test.js       # Timer logic tests
├── .dockerignore               # Docker ignore rules
├── .gitignore                  # Git ignore rules
├── .prettierrc.json            # Code formatting configuration
├── docker-compose.yml          # Docker deployment configuration
├── docker-compose.dev.yml      # Docker development configuration
├── Dockerfile                  # Docker container definition
├── jest.config.js              # Testing framework configuration
├── LICENSE                     # MIT license
├── package.json                # Node.js project configuration
├── package-lock.json           # Dependency lock file
└── README.md                   # Project documentation
```

### Important Notes

#### Shared Files Architecture

**Implementation**: Shared files (`shared/constants.js` and `shared/timer-core.js`) are served to the client through a dedicated Express route:

```javascript
// Serve shared files for client access
this.app.use('/js/shared', express.static(path.join(__dirname, '../shared')));
```

**Client Access**: The HTML references these files at:

- `js/shared/constants.js`
- `js/shared/timer-core.js`

This allows the same code to be shared between the server and client, ensuring consistency in timer calculations and constants across the entire application.

#### Specification Maintenance

This specification document is designed to be a living document that accurately reflects the current state of the FocusTime application. It should be updated whenever:

1. **New Features**: Any new functionality is added to the application
2. **Architecture Changes**: Modifications to the component structure or data flow
3. **API Changes**: Updates to WebSocket message protocols or data structures
4. **Technology Updates**: Changes to dependencies, frameworks, or development tools
5. **Bug Fixes**: When fixes reveal inaccuracies in the documented behavior