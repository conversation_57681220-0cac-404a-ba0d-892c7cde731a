import type { Segment, TimerState, TimerStateInternal } from '../server/formatters.d';

declare class TimerCore {
  segments: Segment[];
  state: TimerStateInternal;

  constructor(segments?: Segment[]);

  start(): TimerStateInternal;
  pause(): TimerStateInternal;
  stop(): TimerStateInternal;
  repeat(repeat?: boolean | null): TimerStateInternal;
  next(): TimerStateInternal;
  resume(): TimerStateInternal;
  sync(): TimerStateInternal;
  updateFromSource(state: TimerState | TimerStateInternal): TimerStateInternal;
  updateSegments(segments: Segment[]): void;
  getState(): TimerStateInternal;
  setState(state: TimerState | TimerStateInternal): void;
}

export default TimerCore;
