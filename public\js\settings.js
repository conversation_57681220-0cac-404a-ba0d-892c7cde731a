/**
 * Settings manager class
 */
class SettingsManager {
  constructor(app = null) {
    this.app = app;
    this.sessionId = null;
    this.isUpdating = false;
    this.collapsedSegments = new Set(); // Track expanded segments
    this.noSleep = new NoSleep();

    this.initialize();
  }

  /**
   * Initialize settings manager
   */
  initialize() {
    this.setupEventListeners();
    this.initializeFocusMode();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    const elements = getSettingsElements();

    Utils.Events.on(elements.settingsBtn, 'click', this.showSettings.bind(this));
    Utils.Events.on(elements.wakeLockBtn, 'click', this.toggleWakeLock.bind(this));
    Utils.Events.on(elements.segmentsBtn, 'click', this.showSegmentsModal.bind(this));
    Utils.Events.on(elements.focusModeBtn, 'click', this.toggleFocusMode.bind(this));

    Utils.Events.on(elements.sessionNameInput, 'blur', Utils.debounce(this.saveSessionSettings.bind(this), 500));
    Utils.Events.on(elements.sessionDescInput, 'blur', Utils.debounce(this.saveSessionSettings.bind(this), 500));

    Utils.Events.on(elements.audioToggle, 'change', this.handleAudioToggle.bind(this));
    Utils.Events.on(elements.repeatToggle, 'change', this.handleRepeatToggle.bind(this));
    Utils.Events.on(elements.wakeLockToggle, 'change', this.handleWakeLockToggle.bind(this));
    Utils.Events.on(elements.focusModeToggle, 'change', this.handleFocusModeToggle.bind(this));

    Utils.Events.on(elements.addSegmentBtn, 'click', this.addSegment.bind(this));
    Utils.Events.on(elements.importBtn, 'click', this.importConfig.bind(this));
    Utils.Events.on(elements.exportBtn, 'click', this.exportConfig.bind(this));
    Utils.Events.on(elements.importFileInput, 'change', this.handleFileImport.bind(this));
  }

  /**
   * Initialize focus mode from stored setting
   */
  initializeFocusMode() {
    const isEnabled = Utils.Storage.getClientSetting('focusMode', false);
    if (isEnabled) this.enableFocusMode();
    this.updateFocusModeDisplay();
  }

  /**
   * Toggle wake lock on/off
   */
  async toggleWakeLock() {
    const isEnabled = this.noSleep.isEnabled;

    if (!isEnabled) {
      await this.noSleep.enable();
    } else {
      await this.noSleep.disable();
    }

    this.updateWakeLockDisplay();
  }

  /**
   * Update wake lock button display based on current state
   */
  updateWakeLockDisplay() {
    const elements = getSettingsElements();
    const isEnabled = this.noSleep.isEnabled;

    // Update button title
    elements.wakeLockBtn.title = isEnabled ? 'Click to disable keep screen awake' : 'Click to keep screen awake';

    // Show/hide disabled line based on wake lock state
    elements.wakeLockDisabledLine.style.display = isEnabled ? 'none' : 'block';
  }

  /**
   * Toggle focus mode on/off
   */
  toggleFocusMode() {
    const isEnabled = Utils.Storage.getClientSetting('focusMode', false);

    // Save to client config (local only)
    Utils.Storage.saveClientSetting('focusMode', !isEnabled);

    // Apply or remove focus mode
    if (!isEnabled) {
      this.enableFocusMode();
    } else {
      this.disableFocusMode();
    }

    this.updateFocusModeDisplay();
  }

  /**
   * Update focus mode button display based on current state
   */
  updateFocusModeDisplay() {
    const elements = getSettingsElements();
    const isEnabled = Utils.Storage.getClientSetting('focusMode', false);

    // Update button title
    elements.focusModeBtn.title = isEnabled ? 'Click to disable focus mode' : 'Click to enable focus mode';

    // Show/hide disabled line based on focus mode state
    elements.focusModeDisabledLine.style.display = isEnabled ? 'none' : 'block';
  }

  /**
   * Update toggle states in settings modal
   */
  updateToggleStates() {
    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    const elements = getSettingsElements();

    // Audio toggle - check if audio is enabled (not muted)
    if (this.app.alerts) {
      elements.audioToggle.checked = this.app.alerts.isUnlocked && this.app.alerts.currentVolumeIndex > 0;
    }

    elements.repeatToggle.checked = session.timer?.repeat || false;
    elements.wakeLockToggle.checked = this.noSleep.isEnabled;
    elements.focusModeToggle.checked = Utils.Storage.getClientSetting('focusMode', false);
  }

  /**
   * Handle audio toggle change
   */
  handleAudioToggle(event) {
    if (this.app.alerts) {
      if (event.target.checked) {
        // Enable audio - set to mid volume if currently muted
        if (this.app.alerts.currentVolumeIndex === 0) {
          this.app.alerts.currentVolumeIndex = 2;
          this.app.alerts.masterVolume = this.app.alerts.volumeLevels[2];
          this.app.alerts.saveVolumeToConfig();
          this.app.alerts.renderVolumeDisplay();
        }
      } else {
        // Disable audio - set to mute
        this.app.alerts.currentVolumeIndex = 0;
        this.app.alerts.masterVolume = 0;
        this.app.alerts.saveVolumeToConfig();
        this.app.alerts.renderVolumeDisplay();
      }
    }
  }

  /**
   * Handle repeat toggle change
   */
  handleRepeatToggle(event) {
    if (this.app.timer) {
      this.app.timer.repeat();
    }
  }

  /**
   * Handle wake lock toggle change
   */
  async handleWakeLockToggle(event) {
    await this.toggleWakeLock();
  }

  /**
   * Handle focus mode toggle change
   */
  handleFocusModeToggle(event) {
    this.toggleFocusMode();
    this.updateToggleStates();
  }

  /**
   * Show settings modal
   */
  showSettings() {
    this.sessionId = this.app.sessions?.getCurrentSessionId();
    this.populateSettings();

    Utils.DOM.showModal('settings-modal');
  }

  /**
   * Show segments modal
   */
  showSegmentsModal() {
    this.sessionId = this.app.sessions?.getCurrentSessionId();
    this.populateSegments();
    Utils.DOM.showModal('segments-modal');
  }
  /**
   * Populate settings with current session data
   */
  populateSettings() {
    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    // Session settings
    const elements = getSettingsElements();
    if (elements.sessionNameInput) elements.sessionNameInput.value = session.name || '';
    if (elements.sessionDescInput) elements.sessionDescInput.value = session.description || '';

    // Update toggle states
    this.updateToggleStates();
  }

  /**
   * Populate segments modal with current session data
   */
  populateSegments() {
    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    // Render segments
    this.renderSegments();
  }

  /**
   * Save session settings
   */
  saveSessionSettings() {
    if (this.isUpdating || !this.sessionId) return;

    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    const elements = getSettingsElements();

    let hasChanges = false;

    if (elements.sessionNameInput.value !== session.name) {
      session.name = elements.sessionNameInput.value.trim();
      hasChanges = true;
    }

    if (elements.sessionDescInput.value !== session.description) {
      session.description = elements.sessionDescInput.value.trim();
      hasChanges = true;
    }

    if (hasChanges) {
      Utils.Storage.saveSession(this.sessionId, session);

      // Sync name and description changes with server
      if (this.app.socket.isSocketConnected()) {
        this.app.socket.updateSegments(this.sessionId);
      }

      this.app.sessions.updateSessionHeader(this.sessionId);

      // Update timer display and repeat mode display
      this.app.timer.renderHtml();
    }
  }

  /**
   * Create segment element
   * @param {Object} segment - Segment data
   * @param {number} index - Segment index
   * @returns {HTMLElement} Segment element
   */
  createSegmentElement(segment, index) {
    const segmentItem = Utils.DOM.create('div', {
      className: 'segment-item collapsed',
      'data-index': index,
    });

    // Check if this segment was previously expanded
    if (this.collapsedSegments.has(index)) {
      segmentItem.classList.add('expanded');
    }

    // Segment header
    const segmentHeader = Utils.DOM.create('div', { className: 'segment-header' });

    const collapseBtn = Utils.DOM.create(
      'button',
      {
        className: 'segment-collapse',
      },
      '▶'
    );

    const nameInput = Utils.DOM.create('input', {
      type: 'text',
      className: 'segment-name',
      value: segment.name,
      placeholder: 'Segment name',
    });

    const durationInput = Utils.DOM.create('input', {
      type: 'number',
      className: 'segment-duration',
      value: Math.floor(segment.duration / 60),
      min: 1,
      max: 1440,
      title: 'Duration (minutes)',
      placeholder: '25',
    });

    const deleteBtn = Utils.DOM.create(
      'button',
      {
        className: 'segment-delete btn-danger btn-small',
        title: 'Delete segment',
      },
      '✖'
    );

    segmentHeader.appendChild(collapseBtn);
    segmentHeader.appendChild(nameInput);
    segmentHeader.appendChild(durationInput);
    segmentHeader.appendChild(deleteBtn);

    // Segment details (initially hidden)
    const segmentDetails = Utils.DOM.create('div', { className: 'segment-details' });

    const alertGroup = Utils.DOM.create('div', { className: 'segment-audio' });
    const alertLabel = Utils.DOM.create('label', {}, 'Alert Sound');
    const alertContainer = Utils.DOM.create('div', {
      className: 'segment-audio-controls',
    });
    const alertSelect = this.createAlertSelect(segment.alert);
    const testAlertBtn = Utils.DOM.create(
      'button',
      {
        type: 'button',
        className: 'test-audio-btn',
        title: 'Test alert sound',
      },
      '🔊'
    );

    alertContainer.appendChild(alertSelect);
    alertContainer.appendChild(testAlertBtn);
    alertGroup.appendChild(alertLabel);
    alertGroup.appendChild(alertContainer);

    const cssText = `/* Body Background */
body {
  background: linear-gradient(45deg, #ffeaa7, #fab1a0);
}

/* Timer Display Styling */
.timer-display {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
  border: none;
}

/* Timer Indicators */
.timer-btn {
  color: #c14242;
}

.timer-btn:hover {
  color: #971e1e;
}`;

    const cssGroup = Utils.DOM.create('div', { className: 'segment-styles' });
    const cssLabel = Utils.DOM.create('label', {}, 'Custom CSS');
    const cssTextarea = Utils.DOM.create('textarea', {
      className: 'segment-css',
      placeholder: cssText,
      value: segment.customCSS,
    });
    const cssHelp = Utils.DOM.create('small', {
      className: 'css-help',
      textContent: 'You can style any element on the page including body, buttons, or the entire interface.',
    });
    cssGroup.appendChild(cssLabel);
    cssGroup.appendChild(cssTextarea);
    cssGroup.appendChild(cssHelp);

    segmentDetails.appendChild(alertGroup);
    segmentDetails.appendChild(cssGroup);

    segmentItem.appendChild(segmentHeader);
    segmentItem.appendChild(segmentDetails);

    // Event handlers
    Utils.Events.on(collapseBtn, 'click', () => this.toggleSegmentCollapsed(segmentItem));
    Utils.Events.on(segmentHeader, 'click', (e) => {
      if (e.target !== collapseBtn && e.target !== deleteBtn && e.target !== nameInput && e.target !== durationInput) {
        this.toggleSegmentCollapsed(segmentItem);
      }
    });

    Utils.Events.on(nameInput, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(
      nameInput,
      'input',
      Utils.debounce(() => this.saveSegmentData(index), 500)
    );

    Utils.Events.on(durationInput, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(
      durationInput,
      'input',
      Utils.debounce(() => this.saveSegmentData(index), 500)
    );

    Utils.Events.on(alertSelect, 'change', () => this.saveSegmentData(index));

    Utils.Events.on(testAlertBtn, 'click', (e) => {
      e.stopPropagation();
      this.testAlert(alertSelect.value);
    });

    Utils.Events.on(cssTextarea, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(
      cssTextarea,
      'input',
      Utils.debounce(() => this.saveSegmentData(index), 1000)
    );

    Utils.Events.on(deleteBtn, 'click', (e) => {
      e.stopPropagation();
      this.deleteSegment(index);
    });

    return segmentItem;
  }

  /**
   * Create alert sound select element
   * @param {string} selectedAlert - Currently selected alert
   * @returns {HTMLElement} Select element
   */
  createAlertSelect(selectedAlert) {
    const select = Utils.DOM.create('select', { className: 'segment-audio-cue' });

    const alerts = this.app.alerts ? this.app.alerts.getAlertSounds() : [{ value: 'Default', name: 'Default Bell' }];

    alerts.forEach((alert) => {
      const option = Utils.DOM.create(
        'option',
        {
          value: alert.value,
          selected: alert.value === selectedAlert,
        },
        alert.name
      );
      select.appendChild(option);
    });

    return select;
  }

  /**
   * Toggle segment expanded state
   * @param {HTMLElement} segmentItem - Segment item element
   */
  toggleSegmentCollapsed(segmentItem) {
    const index = parseInt(segmentItem.getAttribute('data-index'));

    if (segmentItem.classList.toggle('collapsed')) {
      this.collapsedSegments.delete(index);
    } else {
      this.collapsedSegments.add(index);
    }
  }

  /**
   * Test alert sound
   * @param {string} alertType - Alert type to test
   */
  testAlert(alertType) {
    this.app.alerts.playAlert(alertType);
  }

  /**
   * Save segment data
   * @param {number} index - Segment index
   */
  saveSegmentData(index) {
    if (this.isUpdating || !this.sessionId) return;

    const session = Utils.Storage.getSession(this.sessionId);
    if (!session || !session.segments.items[index]) return;

    const segmentItem = Utils.DOM.query(`[data-index="${index}"]`);
    if (!segmentItem) return;

    const nameInput = segmentItem.querySelector('.segment-name');
    const durationInput = segmentItem.querySelector('.segment-duration');
    const alertSelect = segmentItem.querySelector('.segment-audio-cue');
    const cssTextarea = segmentItem.querySelector('.segment-css');

    const segment = session.segments.items[index];
    const oldDuration = segment.duration;

    segment.name = nameInput.value.trim();
    segment.alert = alertSelect.value;
    segment.customCSS = cssTextarea.value;

    const minutes = parseInt(durationInput.value) || 1;
    segment.duration = Math.max(1, Math.min(1440, minutes)) * 60; // Convert to seconds

    session.segments.lastUpdated = Utils.getCurrentTimestamp();

    // Save to localStorage first (optimistic update)
    Utils.Storage.saveSession(this.sessionId, session);

    // Make segments immediately available by dispatching update event
    Utils.Events.dispatch(document, 'segmentsUpdated', {
      sessionId: this.sessionId,
      session: {
        name: session.name,
        description: session.description,
        segments: session.segments,
      },
      source: 'local',
    });

    // Sync with server after localStorage save
    // TODO: Only send if there are changes
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.updateSegments(this.sessionId);
    }

    // Update timer if this is the current segment and duration changed
    // TODO: Refactor and move to timer.js
    if (this.app.timer.getCurrentSession()) {
      const timerState = this.app.timer.getTimerState();
      if (timerState.currentSegment === index && oldDuration !== segment.duration) {
        // Use helper method to update segment duration
        this.app.timer.handleSegmentDuration(index, segment.duration);
        this.app.timer.renderHtml();
      }
    }
  }

  /**
   * Add new segment
   */
  addSegment() {
    if (!this.sessionId) return;

    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    const newSegment = {
      name: 'New Segment',
      duration: 1500, // 25 minutes
      alert: 'Default',
      customCSS: `/* Timer Display Styling */
#timer-display {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  font-size: 4rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
  border: none;
}

/* Timer Indicators */
.timer-btn {
  color: #c14242;
}

.timer-btn:hover {
  color: #971e1e;
}

/* Body Background */
body {
  background: linear-gradient(45deg, #ffeaa7, #fab1a0);
  transition: background 0.5s ease;
}

/* Controls Styling */
.control-btn {
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}`,
    };

    session.segments.items.push(newSegment);
    session.segments.lastUpdated = Utils.getCurrentTimestamp();

    // Save to localStorage first (optimistic update)
    Utils.Storage.saveSession(this.sessionId, session);

    // Make segments immediately available by dispatching update event
    Utils.Events.dispatch(document, 'segmentsUpdated', {
      sessionId: this.sessionId,
      session: {
        name: session.name,
        description: session.description,
        segments: session.segments,
      },
      source: 'local',
    });

    // Re-render segments to show immediate changes
    this.renderSegments();

    // Sync with server after localStorage save
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.updateSegments(this.sessionId);
    }
  }

  /**
   * Delete segment
   * @param {number} index - Segment index to delete
   */
  async deleteSegment(index) {
    if (!this.sessionId) return;

    const session = Utils.Storage.getSession(this.sessionId);
    if (!session || session.segments.items.length <= 1) {
      alert('Cannot delete the last segment');
      return;
    }

    const confirmed = await Utils.showConfirm('Delete this segment?');
    if (confirmed) {
      session.segments.items.splice(index, 1);
      session.segments.lastUpdated = Utils.getCurrentTimestamp();

      // Reset timer if current segment was deleted
      if (this.app.timer && this.app.timer.getCurrentSession()) {
        // Use helper method to handle segment deletion
        this.app.timer.handleSegmentDeletion();
      }

      // Save to localStorage first (optimistic update)
      Utils.Storage.saveSession(this.sessionId, session);

      // Make segments immediately available by dispatching update event
      Utils.Events.dispatch(document, 'segmentsUpdated', {
        sessionId: this.sessionId,
        session: {
          name: session.name,
          description: session.description,
          segments: session.segments,
        },
        source: 'local',
      });

      // Re-render segments to show immediate changes
      this.renderSegments();

      // Sync with server after localStorage save
      if (this.app.socket.isSocketConnected()) {
        this.app.socket.updateSegments(this.sessionId);
      }

      // Update timer display
      this.app.timer.renderHtml();
    }
  }

  /**
   * Export configuration
   */
  exportConfig() {
    if (!this.sessionId) return;

    const config = this.app.sessions?.exportSessionConfig(this.sessionId);
    if (!config) {
      alert('No session data to export');
      return;
    }

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = Utils.DOM.create('a', {
      href: URL.createObjectURL(dataBlob),
      download: `focus-timer-${this.sessionId}.json`,
    });

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(link.href);
  }

  /**
   * Import configuration
   */
  importConfig() {
    const importFileInput = Utils.DOM.getId('import-file-input');
    importFileInput.click();
  }

  /**
   * Handle file import
   * @param {Event} event - File input change event
   */
  handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/json') {
      alert('Please select a valid JSON file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target.result);
        this.importSessionConfig(config);
      } catch (error) {
        alert('Invalid JSON file format');
        console.error('Import error:', error);
      }
    };

    reader.readAsText(file);

    // Reset file input
    event.target.value = '';
  }

  /**
   * Import session configuration
   * @param {Object} config - Configuration object
   */
  importSessionConfig(config) {
    if (!this.sessionId) return;

    try {
      this.app.sessions?.importSessionConfig(config, this.sessionId);
      this.populateSettings();
      alert('Configuration imported successfully');
    } catch (error) {
      alert('Failed to import configuration');
      console.error('Import error:', error);
    }
  }

  /**
   * Update settings data based on event type
   * @param {string} eventType - WebSocket event type
   * @param {Object} response - Event data
   */
  async updateData(eventType, response) {
    // Session data is already updated by Coordinator, read from localStorage
    const sessionId = response.sessionId;
    if (!sessionId) return;

    // Update internal state based on event type
    switch (eventType) {
      case 'sessionCreated':
        this.sessionId = sessionId;
        break;

      case 'segmentsUpdated':
        // Session data already updated by Coordinator
        // Just update sessionId for consistency
        this.sessionId = sessionId;
        break;
    }
  }

  /**
   * Render settings HTML based on current data
   * @param {string} eventType - WebSocket event type
   * @param {Object} eventDetail - Event data
   */
  async renderHtml(eventType, eventDetail) {
    // Handle event-specific rendering
    switch (eventType) {
      case 'sessionCreated':
        // Populate settings if user has the modal open
        if (this.sessionId) {
          this.renderSegments();
          this.populateSettings();
        }
        break;

      case 'segmentsUpdated':
        // Re-render segments
        if (this.sessionId && eventDetail.source === 'server') {
          this.renderSegments();
          this.populateSettings();
        }
        break;

      case 'timerUpdated':
        this.populateSettings();
        break;
    }
  }

  /**
   * Render settings form
   */
  renderSettings() {
    const elements = getSettingsElements();
    const session = Utils.Storage.getSession(this.sessionId);
    if (!session) return;

    this.app.sessions.updateSessionHeader(this.sessionId);

    elements.sessionNameInput.value = session.name;
    elements.sessionDescInput.value = session.description;
  }

  /**
   * Render segments list
   */
  renderSegments() {
    const elements = getSettingsElements();
    const session = Utils.Storage.getSession(this.sessionId);
    if (!session || !session.segments.items) return;

    elements.segmentsList.innerHTML = '';

    session.segments.items.forEach((segment, index) => {
      const segmentElement = this.createSegmentElement(segment, index);
      elements.segmentsList.appendChild(segmentElement);
    });
  }

  /**
   * Enable focus mode with activity tracking
   */
  enableFocusMode() {
    document.body.classList.add('focus-mode');
    this.startActivityTracking();
  }

  /**
   * Disable focus mode and stop activity tracking
   */
  disableFocusMode() {
    document.body.classList.remove('focus-mode', 'show-ui');
    this.stopActivityTracking();
  }

  /**
   * Start tracking user activity for focus mode
   */
  startActivityTracking() {
    this.activityTimeout = null;
    this.isTrackingActivity = true;
    this.isHoveringButton = false;

    // Show UI initially
    document.body.classList.add('show-ui');

    // Activity events to track
    const events = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart', 'touchmove'];

    this.activityHandler = this.resetActivityTimer.bind(this);

    events.forEach((event) => {
      document.addEventListener(event, this.activityHandler, { passive: true });
    });

    // Setup hover detection
    this.setupHoverDetection();

    this.resetActivityTimer();
  }

  /**
   * Setup hover detection for buttons and status bar to prevent UI hiding
   */
  setupHoverDetection() {
    const buttonSelectors = ['.timer-btn', '.control-btn', '.corner-btn', '.status-bar'];

    buttonSelectors.forEach((selector) => {
      const buttons = document.querySelectorAll(selector);
      buttons.forEach((button) => {
        button.addEventListener('mouseenter', () => {
          this.isHoveringButton = true;
          document.body.classList.add('show-ui');
        });

        button.addEventListener('mouseleave', () => {
          this.isHoveringButton = false;
          this.resetActivityTimer();
        });
      });
    });
  }

  /**
   * Stop tracking user activity
   */
  stopActivityTracking() {
    if (!this.isTrackingActivity) return;

    this.isTrackingActivity = false;

    if (this.activityTimeout) {
      clearTimeout(this.activityTimeout);
      this.activityTimeout = null;
    }

    // Remove event listeners
    const events = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart', 'touchmove'];

    if (this.activityHandler) {
      events.forEach((event) => {
        document.removeEventListener(event, this.activityHandler);
      });
      this.activityHandler = null;
    }
  }

  /**
   * Reset activity timer
   */
  resetActivityTimer() {
    if (!this.isTrackingActivity) return;

    // Show UI
    document.body.classList.add('show-ui');

    // Clear existing timeout
    if (this.activityTimeout) {
      clearTimeout(this.activityTimeout);
    }

    // Set new timeout (20 seconds)
    this.activityTimeout = setTimeout(() => {
      if (this.isTrackingActivity && !this.shouldKeepUIVisible()) {
        document.body.classList.remove('show-ui');
      }
    }, 1000);
  }

  /**
   * Check if UI should remain visible
   * @returns {boolean} True if UI should stay visible
   */
  shouldKeepUIVisible() {
    // Don't hide UI if hovering over buttons or status bar
    if (this.isHoveringButton) {
      return true;
    }

    // Don't hide UI if any modal is visible
    const modals = document.querySelectorAll('.modal');
    for (const modal of modals) {
      if (modal.style.display === 'block' || modal.classList.contains('show')) {
        return true;
      }
    }

    // Don't hide UI if any popup is visible
    const popups = document.querySelectorAll('.popup');
    for (const popup of popups) {
      if (!popup.classList.contains('hidden')) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get current session settings
   * @returns {Object} Current session settings
   */
  getCurrentSettings() {
    if (!this.sessionId) return null;

    const session = Utils.Storage.getSession(this.sessionId);
    return session
      ? {
          name: session.name,
          description: session.description,
          repeat: session.timer.repeat,
          segments: session.segments.items,
        }
      : null;
  }
}

/**
 * Retrieve all settings-related DOM elements
 * @returns {Object} Settings DOM elements
 */
const getSettingsElements = () => ({
  settingsBtn: Utils.DOM.getId('settings-btn'),
  wakeLockBtn: Utils.DOM.getId('wake-lock-btn'),
  wakeLockDisabledLine: Utils.DOM.getId('wake-lock-disabled-line'),
  segmentsBtn: Utils.DOM.getId('segment-status'),
  focusModeBtn: Utils.DOM.getId('focus-mode-btn'),
  focusModeDisabledLine: Utils.DOM.getId('focus-mode-disabled-line'),
  sessionNameInput: Utils.DOM.getId('session-name-input'),
  sessionDescInput: Utils.DOM.getId('session-desc-input'),
  audioToggle: Utils.DOM.getId('audio-toggle'),
  repeatToggle: Utils.DOM.getId('repeat-toggle'),
  wakeLockToggle: Utils.DOM.getId('wake-lock-toggle'),
  focusModeToggle: Utils.DOM.getId('focus-mode-toggle'),
  addSegmentBtn: Utils.DOM.getId('add-segment-btn'),
  importBtn: Utils.DOM.getId('import-btn'),
  exportBtn: Utils.DOM.getId('export-btn'),
  importFileInput: Utils.DOM.getId('import-file-input'),
  settingsModal: Utils.DOM.getId('settings-modal'),
  segmentsModal: Utils.DOM.getId('segments-modal'),
  segmentsList: Utils.DOM.getId('segments-list'),
});

const initializeSettingsManager = () => {
  Utils.Events.dispatch(document, 'settingsManagerReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeSettingsManager);
} else {
  initializeSettingsManager();
}

window.SettingsManager = SettingsManager;
