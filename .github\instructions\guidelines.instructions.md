---
applyTo: '**'
---

# Agent Guidelines

General guidelines for coding agents to follow when executing tasks.

## AI/LLM-Specific Guidance

- If a user request is ambiguous or incomplete, ask for clarification before proceeding.
- Do not assume requirements that are not stated.

## Formatting

- ALWAYS use Unix-style line endings (LF).
- NEVER use Windows-style line endings (CRLF).
- NEVER use Mac-style line endings (CR).
- ALWAYS use UTF-8 encoding.
- ALWAYS use spaces for indentation (2 spaces).
- NEVER use tabs for indentation.

## Coding

- DO NOT add functions or methods that will go unused.
- DO NOT add code that is not related to the task at hand.
- DO NOT add code that is not necessary.
- DO NOT add code that is not asked for.
- REMOVE ALL UNUSED CODE WHEN YOU ARE DONE WITH A TASK.

### Refactoring

- Remove old, unused code when refactoring.

### Naming

- Use SHORT, concise but meaningful variable and function names.
- Avoid using abbreviations in variable and function names.
- Avoid using single character variable names except for loop counters.

### Comments

- Use clear, concise, and technical language in comments.
- Keep comments to a minimum and only use comments in code to explain complex logic or non-obvious code.
- Use Prettier for code formatting to maintain consistent style across the codebase.

### Simplicity

- DO NOT OVERCOMPLICATE SOLUTIONS
- REMOVE UNNECESSARY COMPLEXITY
- BASIC IS BETTER THAN COMPLEX
- KEEP YOUR SOLUTIONS SIMPLE
- SIMPLIFY YOUR SOLUTIONS
- USE SIMPLE SOLUTIONS
- USE SIMPLE LOGIC
- USE K.I.S.S. PRINCIPLES

## Security

- DO NOT log sensitive information.
- DO NOT log user credentials.
- DO NOT log user passwords.
- DO NOT log user tokens.
- DO NOT log user API keys.
- NEVER commit secrets, credentials, or sensitive data to the repository.

## Documentation

- Use clear, concise and technical language in documentation.
- Use header comments to explain the purpose of the file.
- Use markdown for documentation.
- Use diagrams and images to help explain complex concepts.
- Use code blocks for code examples.
- Use links to reference external documentation.
- Use links to external resources.
- Use tables to present data.

## Planning

- Output detailed plans before implementing any code.
- If the user explicitly asks you to create a plan document, write the plan to a markdown file in the `docs/plans` directory.

### Plan Structure

The plan should be written as a detailed implemention plan that can be later executed by coding agents. 
Remember to provide enough context to allow coding agents to implement the plan.

Plans should be loosly structured as follows:

- Title of the plan
- Overview of the task
- Checklist of steps to complete the task
- Checklist of files to be created, modified or removed
- Checklist of dependencies to be installed
- Checklist of tests to be created
- Diagrams and images if necessary
- Code snippets
- Summary of the plan

Create headers and subheaders as needed to organize the plan. The plan structure is only a guideline, feel free to deviate from it as needed.
