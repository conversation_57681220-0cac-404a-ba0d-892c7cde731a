/**
 * Core timer calculation logic shared between server and client
 * Supports both CommonJS and browser global patterns
 */

// Import constants based on environment
let _DEFAULT_DURATION;
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  // Node.js environment
  const { DEFAULT_DURATION } = require('./constants');
  _DEFAULT_DURATION = DEFAULT_DURATION;
} else {
  // Browser environment - use global DEFAULT_DURATION from constants.js or fallback
  _DEFAULT_DURATION =
    typeof DEFAULT_DURATION !== 'undefined' ? DEFAULT_DURATION : window.constraints?.DEFAULT_DURATION ?? 1500;
}

/**
 * Timer core class for managing timer state and calculations
 */
class TimerCore {
  /**
   * Create a new TimerCore instance.
   * @param {Array} [segments=[]] - Array of segment objects.
   */
  constructor(segments = []) {
    this.segments = segments;
    this.state = {
      // Shared timer state
      repeat: false,
      currentSegment: 0,
      timeRemaining: (segments[0]?.duration ?? _DEFAULT_DURATION) * 1000,
      isRunning: false,
      isPaused: false,

      // Internal timing state
      startedSegment: 0,
      startedAt: 0,
      pausedAt: 0,
      timePaused: 0,
    };
  }

  /**
   * Start the timer.
   * @returns {Object} Updated timer state.
   */
  start() {
    const timer = this.state;

    // Handle resume from pause
    if (timer.isPaused) {
      this.resume();
    } else if (!timer.isRunning) {
      timer.startedSegment = timer.currentSegment;
      timer.startedAt = Date.now();
      timer.timePaused = 0;
    }

    // Update timer state
    timer.isRunning = true;
    timer.isPaused = false;
    timer.pausedAt = 0;

    return { ...timer };
  }

  /**
   * Pause the timer.
   * @returns {Object} Updated timer state.
   */
  pause() {
    const timer = this.state;

    // Update timer state
    timer.isPaused = true;
    timer.pausedAt = Date.now();

    return { ...timer };
  }

  /**
   * Stop the timer.
   * @returns {Object} Updated timer state.
   */
  stop() {
    const timer = this.state;

    // Reset timer state
    timer.isRunning = false;
    timer.isPaused = false;
    timer.currentSegment = 0;
    timer.timeRemaining = (this.segments[0]?.duration ?? _DEFAULT_DURATION) * 1000;
    timer.startedSegment = 0;
    timer.startedAt = 0;
    timer.pausedAt = 0;
    timer.timePaused = 0;

    return { ...timer };
  }

  /**
   * Toggle repeat mode.
   * @param {boolean} [repeat] - Optional explicit repeat value. If omitted, toggles current value.
   * @returns {Object} Updated timer state.
   */
  repeat(repeat = null) {
    const timer = this.state;
    timer.repeat = Boolean(repeat ?? !timer.repeat);
    return { ...timer };
  }

  /**
   * Move to next segment.
   * @returns {Object} Updated timer state.
   */
  next() {
    const timer = this.state;
    const now = Date.now();

    if (++timer.currentSegment >= this.segments.length) {
      timer.currentSegment = 0;
    }

    timer.timeRemaining = (this.segments[timer.currentSegment]?.duration ?? _DEFAULT_DURATION) * 1000;

    if (timer.isRunning) {
      timer.startedSegment = timer.currentSegment;
      timer.startedAt = now;
      timer.pausedAt = timer.isPaused ? now : 0;
      timer.timePaused = 0;
    }

    return { ...timer };
  }

  /**
   * Handle timer resume after pause.
   * @returns {Object} Updated timer state.
   */
  resume() {
    const now = Date.now();
    const timer = this.state;

    if (timer.isPaused) {
      // Add pause duration to total paused time
      timer.isPaused = false;
      timer.timePaused += now - timer.pausedAt;
      timer.pausedAt = 0;
    }

    return { ...timer };
  }

  /**
   * Calculate current timer state based on elapsed time.
   * @returns {Object} Updated timer state.
   */
  sync() {
    const now = Date.now();
    const timer = this.state;

    // Validate timer state
    if (!timer.isRunning || !timer.startedAt) return { ...timer };

    // Validate segments
    const segments = this.segments;
    if (!segments || segments.length === 0) return { ...timer };

    // Calculate actual elapsed time
    const offset = timer.isPaused && timer.pausedAt > 0 ? now - timer.pausedAt : 0;
    const elapsed = now - timer.startedAt - timer.timePaused - offset;

    // Find current segment starting from startedSegment
    let current = timer.startedSegment;
    if (current >= segments.length) current = 0;

    // Calculate remaining time in the `current` segment
    let remaining = elapsed;
    while (remaining > 0) {
      const duration = segments[current].duration * 1000;
      if (remaining < duration) break;

      remaining -= duration;
      current++;

      // Handle repeat mode wraparound
      if (current >= segments.length && timer.repeat) {
        current = 0;
      }

      // Check if timer should stop (no repeat and reached end)
      if (current >= segments.length && !timer.repeat) {
        timer.isRunning = false;
        timer.isPaused = false;
        timer.currentSegment = 0;
        timer.timeRemaining = segments[0].duration * 1000;
        timer.startedAt = 0;
        timer.startedSegment = 0;
        timer.pausedAt = 0;
        timer.timePaused = 0;
        return { ...timer };
      }
    }

    // Update session state
    timer.currentSegment = current;
    timer.timeRemaining = segments[current].duration * 1000 - remaining;

    return { ...timer };
  }

  /**
   * Update timer state from external source.
   * @param {Object} state - Timer state from external source.
   * @returns {Object} Updated timer state.
   */
  updateFromSource(state) {
    const timer = this.state;
    const now = Date.now();

    // Update timer state from source
    timer.repeat = state.repeat;
    timer.currentSegment = state.currentSegment;
    timer.timeRemaining = state.timeRemaining;
    timer.isRunning = state.isRunning;
    timer.isPaused = state.isPaused;

    // Calculate elapsed time
    const duration = (this.segments[timer.currentSegment]?.duration ?? _DEFAULT_DURATION) * 1000;
    const elapsed = duration - timer.timeRemaining;

    // Reset timer baseline
    timer.startedSegment = timer.currentSegment;
    timer.startedAt = timer.isRunning ? now - elapsed : 0; // Calculate when this segment started by subtracting elapsed time from current time
    timer.pausedAt = timer.isPaused ? now : 0;
    timer.timePaused = 0;

    return { ...timer };
  }

  /**
   * Update segments and adjust timer state accordingly.
   * @param {Array} segments - Array of segment objects.
   */
  updateSegments(segments) {
    this.segments = segments;

    const now = Date.now();
    const timer = this.state;

    if (timer.currentSegment >= segments.length) {
      timer.timeRemaining = (segments[0]?.duration ?? _DEFAULT_DURATION) * 1000;
      timer.currentSegment = 0;
      timer.startedSegment = 0;
      timer.startedAt = timer.startedAt ? now : 0;
      timer.pausedAt = timer.pausedAt ? now : 0;
      timer.timePaused = 0;
      return;
    }

    const duration = (segments[timer.currentSegment]?.duration ?? _DEFAULT_DURATION) * 1000;

    if (timer.isRunning) {
      const elapsed = now - timer.startedAt - timer.timePaused;
      timer.startedAt = now - elapsed;
      timer.startedSegment = timer.currentSegment;
      timer.timePaused = 0;
      timer.pausedAt = timer.isPaused ? now : 0;

      if (timer.timeRemaining > duration) {
        timer.timeRemaining = duration;
        timer.startedAt = now;
      }

      return;
    }

    timer.timeRemaining = duration;
  }

  /**
   * Get current timer state.
   * @returns {Object} Current timer state.
   */
  getState() {
    return { ...this.state };
  }

  /**
   * Set timer state (for initialization).
   * @param {Object} state - Timer state object.
   */
  setState(state) {
    this.state = { ...this.state, ...state };
  }
}

// Dual export pattern
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = TimerCore;
} else {
  window.TimerCore = TimerCore;
}
