// Playwright UI integration tests for FocusTime
// Covers all use cases from SPECIFICATIONS.md
// Uses the real server and real browser (no mocks)

const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3000'; // Adjust if server runs on a different port

test.describe('FocusTime Collaborative Timer - UI Integration', () => {
  test('Session creation and joining via URL', async ({ page, context }) => {
    await page.goto(BASE_URL);
    await expect(page).toHaveURL(/\/[a-z0-9-]{3,64}$/);
    const sessionUrl = page.url();
    const page2 = await context.newPage();
    await page2.goto(sessionUrl);
    await expect(page2).toHaveURL(sessionUrl);
    const timer1 = await page.locator('#timer-text').textContent();
    const timer2 = await page2.locator('#timer-text').textContent();
    expect(timer1).toBe(timer2);
  });

  test('Timer controls: start, pause, stop, next segment', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#start-btn');
    await expect(page.locator('body')).toHaveClass(/timer-running/);
    await page.click('#pause-btn');
    await expect(page.locator('body')).toHaveClass(/timer-paused/);
    await page.click('#stop-btn');
    await expect(page.locator('body')).toHaveClass(/timer-stopped/);
    await page.click('#next-btn');
    // Segment status should update (e.g., 2/2)
    const segmentStatus = await page.locator('#segment-status').textContent();
    expect(segmentStatus).toMatch(/\d+\/\d+/);
  });

  test('Multi-segment support: add and cycle segments', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#segment-status');
    await expect(page.locator('#segments-modal')).toBeVisible();
    await page.click('#add-segment-btn');
    const newSegmentInput = page.locator('#segments-list .segment-name');
    await expect(newSegmentInput.last()).toHaveValue('New Segment');
    // Close only the segments modal
    await page.click('#segments-modal .close');
    await page.click('#next-btn');
    const segmentStatus = await page.locator('#segment-status').textContent();
    expect(segmentStatus).toMatch(/\d+\/\d+/);
  });

  test('Repeat mode: enable and verify toggle', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#repeat-btn');
    await expect(page.locator('body')).toHaveClass(/timer-repeat/);
    await page.click('#repeat-btn');
    await expect(page.locator('body')).not.toHaveClass(/timer-repeat/);
  });

  test('Session sharing: copy URL and QR code', async ({ page }) => {
    await page.goto(BASE_URL);
    // Open modal explicitly
    await page.evaluate(() => window.app.share.showShareModal(window.location.href));
    await expect(page.locator('#share-modal')).toBeVisible();
    await page.click('#share-copy-btn');
    await expect(page.locator('#share-qr-code canvas')).toBeVisible();
  });

  test('User profile: open and update', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#user-profile-btn');
    await expect(page.locator('#user-modal')).toBeVisible();
    await page.fill('#user-name-input', 'Test User');
    // Blur to trigger save
    await page.locator('#user-name-input').blur();
    // Modal stays open, but name is saved in local user object
    // Check that the input value is updated
    await expect(page.locator('#user-name-input')).toHaveValue('Test User');
  });

  test('Settings: toggle focus mode, wake lock, audio, repeat', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#settings-btn');
    await expect(page.locator('#settings-modal')).toBeVisible();
    await page.click('#focus-mode-toggle + .toggle-slider');
    await page.click('#audio-toggle + .toggle-slider');
    await page.click('#repeat-toggle + .toggle-slider');
    try {
      await page.click('#wake-lock-toggle + .toggle-slider');
    } catch (e) {}
    await expect(page.locator('#focus-mode-toggle')).toBeChecked();
    await expect(page.locator('#repeat-toggle')).toBeChecked();
    // Do not check #audio-toggle as browser may block programmatic audio enable
  });

  test('Connected users: see and update user list', async ({ page, context }) => {
    await page.goto(BASE_URL);
    await page.click('#connected-users');
    await expect(page.locator('#users-popup')).toBeVisible();
    const page2 = await context.newPage();
    await page2.goto(page.url());
    // Clear storage to force new user (must be on app origin)
    await page2.evaluate(() => { localStorage.clear(); sessionStorage.clear(); });
    await page2.reload();
    // Wait for both users to appear (retry for up to 30s)
    await page.reload();
    const usersList = page.locator('#users-list .user-item');
    await expect(usersList).toHaveCount(2, { timeout: 30000 });
  });
});

test.describe('Advanced and Edge-case UI Integration Scenarios', () => {
  test('Change segment while timer is running', async ({ page, context }) => {
    await page.goto(BASE_URL);
    await page.click('#start-btn');
    await expect(page.locator('body')).toHaveClass(/timer-running/);
    const initialSegment = await page.locator('#segment-status').textContent();
    await page.click('#next-btn');
    const newSegment = await page.locator('#segment-status').textContent();
    expect(newSegment).not.toBe(initialSegment);
    // Open another client and check sync
    const page2 = await context.newPage();
    await page2.goto(page.url());
    await expect(page2.locator('#segment-status')).toHaveText(newSegment);
  });

  test('Modify segments while timer is running', async ({ page, context }) => {
    await page.goto(BASE_URL);
    await page.click('#start-btn');
    await expect(page.locator('body')).toHaveClass(/timer-running/);
    await page.click('#segment-status');
    await expect(page.locator('#segments-modal')).toBeVisible();
    // Edit current segment name
    const nameInput = page.locator('#segments-list .segment-name').first();
    await nameInput.fill('Edited Segment');
    await page.click('#segments-modal .close');
    // Check update in real time on another client
    const page2 = await context.newPage();
    await page2.goto(page.url());
    await expect(page2.locator('#segments-list .segment-name').first()).toHaveValue('Edited Segment');
    // Add a new segment
    await page.click('#segment-status');
    await page.click('#add-segment-btn');
    await page.click('#segments-modal .close');
    await expect(page2.locator('#segments-list .segment-name')).toHaveCount(2);
    // Delete a segment
    await page.click('#segment-status');
    await page.click('#segments-list .delete-segment-btn').last();
    await page.click('#segments-modal .close');
    await expect(page2.locator('#segments-list .segment-name')).toHaveCount(1);
  });

  test('Settings, segments, and user profile sync in real time', async ({ page, context }) => {
    await page.goto(BASE_URL);
    const page2 = await context.newPage();
    await page2.goto(page.url());
    // Change settings in page1
    await page.click('#settings-btn');
    await page.click('#focus-mode-toggle + .toggle-slider');
    await page.click('#settings-modal .close');
    // Check sync in page2
    await page2.click('#settings-btn');
    await expect(page2.locator('#focus-mode-toggle')).toBeChecked();
    await page2.click('#settings-modal .close');
    // Change user profile in page1
    await page.click('#user-profile-btn');
    await page.fill('#user-name-input', 'Sync User');
    await page.locator('#user-name-input').blur();
    await page.click('#user-modal .close');
    // Check sync in page2
    await page2.click('#user-profile-btn');
    await expect(page2.locator('#user-name-input')).toHaveValue('Sync User');
  });

  test('Timer/server sync logic on join', async ({ page, context }) => {
    await page.goto(BASE_URL);
    await page.click('#start-btn');
    // Wait a second to ensure timer is running
    await page.waitForTimeout(1000);
    const runningTime = await page.locator('#timer-text').textContent();
    // Open a new client and join same session
    const page2 = await context.newPage();
    await page2.goto(page.url());
    // Timer should be running and match (or be close)
    const runningTime2 = await page2.locator('#timer-text').textContent();
    expect(runningTime2).not.toBe('00:00');
    // Pause in page2, check sync in page1
    await page2.click('#pause-btn');
    await expect(page.locator('body')).toHaveClass(/timer-paused/);
  });

  test('Simultaneous edits: segments/settings', async ({ page, context }) => {
    await page.goto(BASE_URL);
    const page2 = await context.newPage();
    await page2.goto(page.url());
    // Both open segments modal
    await page.click('#segment-status');
    await page2.click('#segment-status');
    // Both edit segment name
    await page.locator('#segments-list .segment-name').first().fill('User1 Edit');
    await page2.locator('#segments-list .segment-name').first().fill('User2 Edit');
    await page.click('#segments-modal .close');
    await page2.click('#segments-modal .close');
    // Final state is consistent (either User1 or User2 edit wins, but not both)
    const name1 = await page.locator('#segments-list .segment-name').first().inputValue();
    const name2 = await page2.locator('#segments-list .segment-name').first().inputValue();
    expect([name1, name2]).toContain(name1);
    expect(name1).toBe(name2);
  });

  test('Network interruption and reconnect', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.click('#start-btn');
    // Simulate offline
    await page.context().setOffline(true);
    await page.waitForTimeout(1000);
    // Make a change while offline
    await page.click('#pause-btn');
    // Go back online
    await page.context().setOffline(false);
    await page.waitForTimeout(2000);
    // Timer state should be restored and synchronized
    await expect(page.locator('body')).toHaveClass(/timer-paused/);
  });

  test('Audio failure does not break timer', async ({ page }) => {
    await page.goto(BASE_URL);
    // Simulate audio context failure
    await page.evaluate(() => {
      window.AlertSystem.prototype.playAlert = async () => { throw new Error('Audio failed'); };
    });
    await page.click('#start-btn');
    await page.click('#next-btn');
    // Timer should continue running
    await expect(page.locator('body')).toHaveClass(/timer-running/);
  });
});
