/**
 * Type definitions for server.ts (Express + WebSocket server)
 */

import { Express } from 'express';
import { Server as HttpServer, IncomingMessage } from 'http';
import { Server as WebSocketServer, WebSocket, RawData } from 'ws';
import type SessionManager from './sessions.d';

export interface ServerWebSocket extends WebSocket {
  isAlive?: boolean;
  sessionId?: string | null;
  clientId?: string | null;
}

/**
 * TimerServer class for collaborative timer server
 */
export class TimerServer {
  app: Express;
  server: HttpServer;
  wss: WebSocketServer | null;
  sessionManager: SessionManager;
  port: number;
  wsPort: number | string;

  constructor();
  setupExpress(): void;
  setupWebSocket(): void;
  setupRoutes(): void;
  setupErrorHandling(): void;
  handleWebSocketConnection(ws: ServerWebSocket, req: IncomingMessage): void;
  handleWebSocketMessage(ws: ServerWebSocket, message: RawData): void;
  handleWebSocketClose(ws: ServerWebSocket, code: number, reason: string): void;
  sendError(ws: ServerWebSocket, message: string): void;
  start(): void;
  stop(signal: string): void;
}

export interface HealthStats {
  status: string;
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  connections: number;
}

export type { Express, HttpServer, IncomingMessage, WebSocketServer, WebSocket };
