{"name": "focustime", "version": "1.0.0", "description": "Collaborative Focus Timer", "type": "module", "main": "server/server.js", "scripts": {"start": "ts-node server/server.ts", "dev": "nodemon --exec ts-node server/server.ts", "test": "jest", "test:timing": "jest tests/timing", "test:server": "jest tests/server", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "dependencies": {"dotenv": "^16.3.1", "express": "^4.18.2", "ws": "^8.14.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@playwright/test": "^1.53.1", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.23", "@types/node": "^20.19.1", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^22.1.0", "nodemon": "^3.0.1", "playwright": "^1.53.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "keywords": ["pomodoro", "timer", "focus", "collaboration", "websocket"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "engines": {"node": ">=16.0.0"}}