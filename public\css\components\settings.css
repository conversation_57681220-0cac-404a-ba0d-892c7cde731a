/* ========================================
 * SETTINGS COMPONENTS
 * ======================================== */

/* Settings button */
.setting-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ========================================
 * SEGMENTS SECTION
 * ======================================== */

/* Segments header */
.segments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.segments-container h4 {
  margin: 0;
}

/* ========================================
 * SEGMENTS ACTIONS
 * ======================================== */

/* Actions container */
.segments-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.4rem;
}

/* Action buttons */
.add-btn,
.export-btn,
.import-btn {
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-md);
  background: var(--color-success);
  color: white;
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: background var(--transition-fast);
}

.add-btn:hover,
.export-btn:hover {
  background: var(--color-success-hover);
}

.import-btn {
  background: var(--color-primary);
}

.import-btn:hover {
  background: var(--color-primary-hover);
}

/* ========================================
 * IMPORT FILE INPUT
 * ======================================== */

.import-file-input {
  display: none;
}

/* ========================================
 * SETTINGS SECTIONS
 * ======================================== */

.settings-section {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border);
}

.settings-section h4 {
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-text-dark);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

/* ========================================
 * TOGGLE SWITCHES
 * ======================================== */

.toggle-group {
  margin-bottom: var(--space-lg);
}

.toggle-group:last-of-type {
  margin-bottom: 0;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
}

.toggle-text {
  color: var(--color-text-dark);
  font-weight: 600;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 26px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border-muted);
  border-radius: 26px;
  transition: var(--transition-normal);
  cursor: pointer;
}

.toggle-slider:before {
  position: absolute;
  content: '';
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: var(--transition-normal);
}

.toggle-input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(24px);
}
