---
applyTo: '**'
---

# Project Guidelines

Agent guidelines for the FocusTime project.

## Coding

- DO NOT create any migration code when refactoring the codebase.
- Follow consistent naming conventions (camelCase for variables and functions).

## Technology Stack

- DO NOT use any client third-party libraries or frameworks (e.g., <PERSON>act, Vue, Angular)
- Keep the application lightweight and focused on the core functionality.
- Use modular code structure, separating concerns into different files (e.g., timer logic, settings, user management).

## Building and Testing

LLM agents should use the following CLI commands to manage the application:

- `npm start`: Start the development server.
- `npm run build`: Build the application for production.
- `npm test`: Run tests.
- `npx playwright test`: Run end-to-end tests.

Always change to the root directory of the project before executing these commands.

- DO NOT AUTOMATICALLY RUN THESE COMMANDS.
- YOU MAY RUN THESE COMMANDS ON REQUEST.

## Server Management

- An instance of the server is always running!
- DO NOT try to start the server!
- DO NOT run test commands!

## File Structure

This is the general file structure of the project. The file structure is constantly changing so use this as a base guide.

focustime/
├── .git/                       # Git repository data
├── .github/
│   ├── instructions/           # Agent guidelines
│   └── workflows/              # GitHub Actions workflows
├── .vscode/                    # VS Code workspace settings
├── docs/
│   └── SPECIFICATIONS.md       # Specifications for the project
├── node_modules/               # Node.js dependencies
├── public/
│   ├── css/
│   │   ├── components/         # Component-specific styles
│   │   └── styles.css          # Main stylesheet
│   ├── js/
│   │   ├── libs/               # Third-party libraries
│   │   │   ├── nosleep/        # Wake lock functionality
│   │   │   └── qr-creator/     # QR code generation
│   │   ├── alerts.js           # Audio system and alert management
│   │   ├── app.js              # Main application orchestrator
│   │   ├── coordinator.js      # Event coordination system
│   │   ├── sessions.js         # Session management and navigation
│   │   ├── settings.js         # Settings and segments configuration
│   │   ├── share.js            # Session sharing functionality
│   │   ├── timer.js            # Timer display and controls
│   │   ├── user.js             # User profile management
│   │   ├── utils.js            # Utility functions and storage
│   │   └── websocket.js        # WebSocket client communication
│   ├── admin.html              # Admin/stats interface
│   ├── favicon.ico             # Application icon
│   └── index.html              # Main application interface
├── server/
│   ├── .env                    # Environment configuration
│   ├── .env.example            # Environment template
│   ├── constants.js            # Server constants and constraints
│   ├── formatters.js           # Message and data formatting
│   ├── server.js               # Main Express + WebSocket server
│   ├── sessions.js             # Session management and coordination
│   ├── timer.js                # Server-side timer logic
│   └── validators.js           # Input validation and security
├── shared/
│   ├── constants.js            # Shared constants between client/server
│   └── timer-core.js           # Shared timer calculation logic
├── tests/
│   ├── playwright/
│   │   └── ui.spec.js          # Playwright UI tests
│   ├── server/
│   │   └── timer.test.js       # Timer logic tests
│   └── setup.js                # Test configuration
├── .dockerignore               # Docker ignore rules
├── .gitignore                  # Git ignore rules
├── .prettierrc.json            # Code formatting configuration
├── docker-compose.dev.yml      # Docker development configuration
├── docker-compose.yml          # Docker deployment configuration
├── Dockerfile                  # Docker container definition
├── jest.config.js              # Testing framework configuration
├── LICENSE                     # MIT license
├── package-lock.json           # Dependency lock file
├── package.json                # Node.js project configuration
├── playwright.config.js        # Playwright configuration
└── README.md                   # Project documentation

## .env Handling

- Never commit real secrets or credentials in .env files.