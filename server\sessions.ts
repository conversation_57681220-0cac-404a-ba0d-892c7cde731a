/**
 * Session management for the collaborative timer server (TypeScript)
 */

import type { ServerWebSocket } from './server.d';
import type {
  IncomingMessage,
  JoinSessionMessage,
  TimerRepeatMessage,
  TimerUpdateMessage,
  UserUpdateMessage,
  SessionInternal,
  UserInternal,
  OutgoingMessage,
  ErrorMessage,
  SegmentsUpdatedMessage,
  UserUpdated,
} from './formatters.d';
import {
  hashString,
  formatIncoming,
  formatClientId,
  formatSessionCreatedMsg,
  formatSessionJoinedMsg,
  formatUserConnectedMsg,
  formatUserDisconnectedMsg,
  formatUserUpdatedMsg,
  formatSegmentsUpdatedMsg,
  formatTimerUpdatedMsg,
  formatConnectedUsersMsg,
  formatInternalSession,
  formatInternalUser,
  formatErrorMsg,
} from './formatters';

/**
 * Session manager class for handling real-time collaboration
 */
class SessionManager implements SessionManager {
  private sessions: Map<string, SessionInternal>;
  private messageHandlers: Map<string, (ws: ServerWebSocket, message: IncomingMessage) => void>;
  private cleanupInterval: NodeJS.Timeout | null;

  constructor() {
    this.sessions = new Map();
    this.messageHandlers = new Map();
    this.cleanupInterval = null;

    this.setupMessageHandlers();
    this.startCleanupTimer();
  }

  /**
   * Setup message handlers for different message types
   */
  private setupMessageHandlers(): void {
    this.messageHandlers.set('join_session', this.handleJoinSession.bind(this));
    this.messageHandlers.set('segments_update', this.handleSegmentsUpdate.bind(this));
    this.messageHandlers.set('timer_start', this.handleTimerStart.bind(this));
    this.messageHandlers.set('timer_pause', this.handleTimerPause.bind(this));
    this.messageHandlers.set('timer_stop', this.handleTimerStop.bind(this));
    this.messageHandlers.set('timer_repeat', this.handleTimerRepeat.bind(this));
    this.messageHandlers.set('timer_next_segment', this.handleTimerNextSegment.bind(this));
    this.messageHandlers.set('timer_update', this.handleTimerUpdate.bind(this));
    this.messageHandlers.set('user_update', this.handleUserUpdate.bind(this));
    this.messageHandlers.set('get_connected_users', this.handleGetConnectedUsers.bind(this));
  }

  /**
   * Handle incoming WebSocket message
   *
   * @param ws WebSocket connection
   * @param message Parsed message object
   */
  handleMessage(ws: ServerWebSocket, message: IncomingMessage): void {
    try {
      const parsed = formatIncoming(message) as IncomingMessage;
      const handler = this.messageHandlers.get(parsed.type);
      if (handler) {
        handler(ws, parsed);
      } else {
        console.warn(`Unknown message type: ${parsed.type}`);
        this.sendError(ws, 'Unknown message type');
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error(`Error handling message ${(message as { type?: string })?.type}:`, error);
        this.sendError(ws, `Failed to process ${(message as { type?: string })?.type}`);
      } else {
        console.error('Unknown error type in handleMessage');
        this.sendError(ws, 'Unknown error');
      }
    }
  }

  /**
   * Handle join session request
   *
   * @param ws WebSocket connection
   * @param message Join session message
   */
  private handleJoinSession(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'join_session') return;
    const { sessionId, user } = message as JoinSessionMessage;

    ws.sessionId = sessionId;
    ws.clientId = formatClientId(user.clientId) as string;

    let session = this.getSession(ws.sessionId as string);
    const isNew = !session;

    if (isNew) session = this.createSession(ws.sessionId as string);
    if (!session) return;

    session.timer = session.timerInstance.sync();

    const now = Date.now();
    const clientId = ws.clientId as string;
    const existing = session.users.get(clientId) as UserInternal;

    if (existing) {
      Object.assign(
        existing,
        formatInternalUser({
          ...user,
          clientId: existing.clientId,
          offlineAt: null,
          lastPing: now,
          ws,
        }) as UserInternal
      );
      session.users.set(clientId, existing);
      console.log(`Client ${clientId} reconnected to session ${ws.sessionId}`);

      const hasOffline = Array.from(session.users.values()).some((u) => !u.ws || u.ws.readyState !== 1);
      if (!hasOffline && session.emptyAt) {
        session.emptyAt = null;
        console.log(`Session ${sessionId} no longer marked for cleanup (user reconnected)`);
      }
    } else {
      const hashedId = hashString(clientId);
      session.users.set(
        clientId,
        formatInternalUser({
          clientId: hashedId,
          name: user.name,
          avatarUrl: user.avatarUrl,
          isOnline: true,
          offlineAt: null,
          lastPing: now,
          ws,
        })
      );
      console.log(`Client ${ws.clientId} joined session ${ws.sessionId}`);
    }

    this.setSession(ws.sessionId as string, session);
    this.sendMessage(
      ws,
      isNew
        ? formatSessionCreatedMsg({
            sessionId: ws.sessionId,
            clientId: ws.clientId,
          })
        : formatSessionJoinedMsg({
            sessionId: ws.sessionId,
            clientId: ws.clientId,
            session,
          })
    );
    this.broadcastToSession(
      session,
      formatUserConnectedMsg({
        sessionId: session.sessionId,
        user: session.users.get(clientId) as UserInternal,
      }),
      clientId
    );
  }

  /**
   * Handle segments update
   *
   * @param ws WebSocket connection
   * @param message Segments update message
   */
  private handleSegmentsUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'segments_update') return;
    const { session: update } = message;

    const session = this.getClientSession(ws);
    if (!session) return;

    if (!update.segments.items || !Array.isArray(update.segments.items)) {
      return this.sendError(ws, 'Invalid segments data');
    }

    session.name = update.name;
    session.description = update.description;
    session.segments = update.segments;
    session.timerInstance.updateSegments(session.segments.items);
    session.timer = session.timerInstance.getState();

    this.setSession(session.sessionId, session);
    this.broadcastToSession(session, formatSegmentsUpdatedMsg(session) as SegmentsUpdatedMessage, ws.clientId);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Segments updated for session ${ws.sessionId}`);
  }

  /**
   * Handle timer start
   *
   * @param ws WebSocket connection
   */
  private handleTimerStart(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_start') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.start();

    this.setSession(ws.sessionId as string, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Timer started for session ${ws.sessionId}`);
  }

  /**
   * Handle timer pause
   *
   * @param ws WebSocket connection
   */
  private handleTimerPause(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_pause') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.pause();

    this.setSession(session.sessionId as string, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Timer paused for session ${ws.sessionId}`);
  }

  /**
   * Handle timer stop
   *
   * @param ws WebSocket connection
   */
  private handleTimerStop(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_stop') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.stop();

    this.setSession(session.sessionId as string, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Timer stopped for session ${ws.sessionId}`);
  }

  /**
   * Handle timer repeat toggle
   *
   * @param ws WebSocket connection
   * @param message Timer repeat message
   */
  private handleTimerRepeat(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_repeat') return;
    const { repeat } = message as TimerRepeatMessage;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.repeat(repeat);

    this.setSession(session.sessionId, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Timer repeat ${session.timer.repeat ? 'enabled' : 'disabled'} for session ${ws.sessionId}`);
  }

  /**
   * Handle timer next segment
   *
   * @param ws WebSocket connection
   */
  private handleTimerNextSegment(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_next_segment') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.next();

    this.setSession(session.sessionId, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Next segment for session ${ws.sessionId} - now on segment ${session.timer.currentSegment + 1}`);
  }

  /**
   * Handle timer update (from client localStorage)
   *
   * @param ws WebSocket connection
   * @param message Timer update message
   */
  private handleTimerUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_update') return;
    const { timer } = message as TimerUpdateMessage;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.updateFromSource(timer);

    this.setSession(session.sessionId as string, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    console.log(`Timer state updated from client ${ws.clientId} for session ${ws.sessionId}`);
  }

  /**
   * Handle user update
   *
   * @param ws WebSocket connection
   * @param message User update message
   */
  private handleUserUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'user_update') return; // TODO: Are these guards necessary?
    const { user } = message as UserUpdateMessage;

    const session = this.getClientSession(ws);
    if (!session || !session.users.has(ws.clientId as string)) return;

    const clientId = ws.clientId as string;
    const existing = session.users.get(clientId) as UserInternal;

    existing.name = user.name;
    existing.avatarUrl = user.avatarUrl;

    session.users.set(clientId, existing);

    this.setSession(session.sessionId, session);
    this.broadcastToSession(
      session,
      formatUserUpdatedMsg({
        sessionId: session.sessionId,
        user: existing,
      } as UserUpdated),
      clientId
    );

    console.log(`User ${clientId} updated profile in session ${ws.sessionId}`);
  }

  /**
   * Handle get connected users request
   *
   * @param ws WebSocket connection
   */
  private handleGetConnectedUsers(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'get_connected_users') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    this.sendMessage(ws, formatConnectedUsersMsg(session));

    console.log(`Connected users list sent to client ${ws.clientId} in session ${ws.sessionId}`);
  }

  /**
   * Create new session
   *
   * @param sessionId Session ID
   * @returns New session object
   */
  private createSession(sessionId: string): SessionInternal {
    const session = formatInternalSession({
      sessionId,
    });

    session.timerInstance.setState(session.timer);

    this.setSession(session.sessionId, session);

    return session;
  }

  /**
   * Set session by ID
   *
   * @param sessionId Session ID
   * @param session Session object
   */
  private setSession(sessionId: string | null | undefined, session: SessionInternal): void {
    if (!sessionId) return; // TODO: Raise error if sessionId is falsy or create new session?
    this.sessions.set(sessionId, formatInternalSession(session));
  }

  /**
   * Get session by ID
   *
   * @param sessionId Session ID
   * @returns Session object or null
   */
  getSession(sessionId: string | null | undefined): SessionInternal | null {
    if (!sessionId) return null; // TODO: Raise error if sessionId is falsy?
    // TODO: Raise error if sessionId does not exist or create new session?
    return this.sessions.has(sessionId) ? this.sessions.get(sessionId)! : null;
  }

  /**
   * Get session for WebSocket client
   *
   * @param ws WebSocket connection
   * @returns Session object or null
   */
  private getClientSession(ws: ServerWebSocket): SessionInternal | null {
    if (ws.sessionId && this.sessions.has(ws.sessionId)) {
      const session = this.getSession(ws.sessionId);
      if (!session) return null;

      session.lastActivity = Date.now();
      this.setSession(session.sessionId, session);

      return session;
    }

    // TODO: Raise error if session does not exist?
    this.sendError(ws, 'Session not found');

    return null;
  }

  /**
   * Start cleanup timer for inactive sessions and offline users
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSessions();
      this.cleanupOfflineUsers();
    }, parseInt(process.env.SESSION_CLEANUP_INTERVAL as string) || 300000);
  }

  /**
   * Clean up inactive sessions
   */
  private cleanupInactiveSessions(): void {
    const now = Date.now();
    const timeout = 10 * 60 * 1000;
    let count = 0;

    this.sessions.forEach((session: SessionInternal, sessionId: string) => {
      let cleanUp = false;

      const online = Array.from(session.users.values()).filter(
        (u: UserInternal) => u.ws && u.ws.readyState === 1
      ).length;

      if (online === 0 && session.emptyAt && now - session.emptyAt > timeout) {
        cleanUp = true;
      }

      if (cleanUp) {
        this.sessions.delete(sessionId);
        count++;
      }
    });

    if (count > 0) console.log(`Cleaned up ${count} inactive sessions`);
  }

  /**
   * Track users who go offline based on WebSocket state
   */
  private trackOfflineUsers(): void {
    const now = Date.now();

    this.sessions.forEach((session: SessionInternal) => {
      session.users.forEach((user: UserInternal) => {
        const isOnline = user.ws && user.ws.readyState === 1;
        if (!isOnline && !user.offlineAt) {
          user.offlineAt = now;
        } else if (isOnline && user.offlineAt) {
          user.offlineAt = null;
        }
      });
    });
  }

  /**
   * Clean up users who have been offline for too long
   */
  private cleanupOfflineUsers(): void {
    this.trackOfflineUsers();

    const now = Date.now();
    const timeout = parseInt(process.env.DISCONNECTED_USER_TIMEOUT as string) || 300000;
    let removed = 0;

    this.sessions.forEach((session: SessionInternal, sessionId: string) => {
      const remove: string[] = [];

      session.users.forEach((user: UserInternal, clientId: string) => {
        if (user.offlineAt && now - user.offlineAt > timeout) {
          remove.push(clientId);
        }
      });

      remove.forEach((clientId: string) => {
        const user = session.users.get(clientId) as UserInternal;
        session.users.delete(clientId);

        removed++;

        console.log(`Removed offline user ${clientId} from session ${sessionId} after timeout`);

        this.broadcastToSession(
          session,
          formatUserDisconnectedMsg({
            sessionId,
            user,
          })
        );
      });

      if (session.users.size === 0 && !session.emptyAt) {
        session.emptyAt = Date.now();
        console.log(`Session ${sessionId} marked for cleanup (all users removed after timeout)`);
      }
    });

    if (removed > 0) console.log(`Cleaned up ${removed} offline users`);
  }

  /**
   * Remove client from session (clear WebSocket reference, cleanup happens later)
   *
   * @param sessionId Session ID
   * @param clientId Client ID
   */
  removeClient(sessionId: string, clientId: string): void {
    const session = this.getSession(sessionId);
    if (!session) return;

    const user = session.users.get(clientId) as UserInternal;
    if (user) {
      user.offlineAt = Date.now();
      user.ws = null;

      this.broadcastToSession(
        session,
        formatUserUpdatedMsg({
          sessionId,
          user,
        }),
        clientId
      );

      console.log(
        `Client ${clientId} disconnected from session ${sessionId}, will be removed after timeout if not reconnected`
      );

      const hasOnline = Array.from(session.users.values()).some((u: UserInternal) => u.ws && u.ws.readyState === 1);
      if (!hasOnline && !session.emptyAt) {
        session.emptyAt = Date.now();
        console.log(`Session ${sessionId} marked for cleanup (all users offline)`);
      }

      session.users.set(clientId, user);
      this.setSession(sessionId, session);
    }
  }

  /**
   * Broadcast timer update to all clients in session
   *
   * @param session Session object
   * @param exclude Client ID to exclude from broadcast
   */
  private broadcastTimerUpdate(session: SessionInternal, exclude: string | null | undefined = null): void {
    session.timer = session.timerInstance.sync();
    this.broadcastToSession(session, formatTimerUpdatedMsg(session), exclude);
  }

  /**
   * Broadcast message to all clients in a session
   *
   * @param sessionId Session ID
   * @param message Message to broadcast
   * @param exclude Client ID to exclude from broadcast
   */
  private broadcastToSession(
    session: SessionInternal,
    message: OutgoingMessage,
    exclude: string | null | undefined = null
  ): void {
    session.users.forEach((user: UserInternal, clientId: string) => {
      if (clientId !== exclude && user.ws && user.ws.readyState === 1) {
        this.sendMessage(user.ws, message);
      }
    });
  }

  /**
   * Send message to WebSocket client
   *
   * @param ws WebSocket connection
   * @param message Message to send
   */
  private sendMessage(ws: ServerWebSocket, message: OutgoingMessage | ErrorMessage): void {
    if (ws.readyState !== 1) return;

    try {
      ws.send(JSON.stringify(message));
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
    }
  }

  /**
   * Send error message to WebSocket client
   *
   * @param ws WebSocket connection
   * @param message Error message
   */
  private sendError(ws: ServerWebSocket, message: string): void {
    const error = { message } as ErrorMessage;
    this.sendMessage(ws, formatErrorMsg(error));
  }

  /**
   * Dispose session manager
   */
  dispose(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.sessions.clear();
    this.messageHandlers.clear();
  }
}

export default SessionManager;
