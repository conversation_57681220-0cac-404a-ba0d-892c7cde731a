/**
 * Coordinator class for managing WebSocket events and component updates
 * Acts as the single listener for all WebSocket events and coordinates component updates
 */
class Coordinator {
  constructor(app = null) {
    this.app = app;
    this.components = new Map();
    this.eventHandlers = new Map();

    this.initialize();
  }

  /**
   * Initialize coordinator
   */
  initialize() {
    this.setupEventListeners();
  }

  /**
   * Setup WebSocket event listeners to intercept all events
   */
  setupEventListeners() {
    Utils.Events.on(document, 'sessionCreated', this.handleEvent.bind(this));
    Utils.Events.on(document, 'sessionJoined', this.handleEvent.bind(this));
    Utils.Events.on(document, 'segmentsUpdated', this.handleEvent.bind(this));
    Utils.Events.on(document, 'timerUpdated', this.handleEvent.bind(this));
    Utils.Events.on(document, 'userConnected', this.handleEvent.bind(this));
    Utils.Events.on(document, 'userDisconnected', this.handleEvent.bind(this));
    Utils.Events.on(document, 'userUpdated', this.handleEvent.bind(this));
    Utils.Events.on(document, 'connectedUsers', this.handleEvent.bind(this));
    Utils.Events.on(document, 'websocketConnected', this.handleEvent.bind(this));
    Utils.Events.on(document, 'websocketDisconnected', this.handleEvent.bind(this));
  }

  /**
   * Initialize the event coordinator
   *
   * Called on websocketConnected event
   */
  initializeComponents() {
    if (this.components.size > 0) return;

    this.registerComponent('user', this.app.user, ['sessionCreated', 'sessionJoined']);
    this.registerComponent('sessions', this.app.sessions, [
      'sessionCreated',
      'sessionJoined',
      'userConnected',
      'userDisconnected',
      'userUpdated',
      'connectedUsers',
    ]);
    this.registerComponent('timer', this.app.timer, [
      'sessionCreated',
      'sessionJoined',
      'segmentsUpdated',
      'timerUpdated',
    ]);
    this.registerComponent('settings', this.app.settings, ['sessionCreated', 'segmentsUpdated', 'timerUpdated']);
    this.registerComponent('app', this.app, ['websocketConnected', 'websocketDisconnected']);

    Utils.Events.dispatch(document, 'componentsReady');

    console.log('Event coordinator initialized with components');
  }

  /**
   * Register a component for event coordination
   * @param {string} name - Component name
   * @param {Object} component - Component instance with updateData and renderHtml methods
   * @param {string[]} events - Array of event types this component should handle
   */
  registerComponent(name, component, events) {
    this.components.set(name, {
      instance: component,
      events: new Set(events),
    });
  }

  /**
   * Process a single event through the coordination pipeline
   * @param {Object} event - Event object
   */
  async handleEvent(event) {
    const { type, detail: data } = event;

    try {
      await this.updateData(type, data);
      await this.updateComponents(type, data);
    } catch (error) {
      console.error(`Error processing ${type} event:`, error);
    }
  }

  /**
   * Update session data and localStorage before component updates
   * This ensures all components read from consistent, up-to-date session state
   * @param {string} type - WebSocket event type
   * @param {Object} data - Event data
   */
  async updateData(type, data) {
    // Initialize components on websocketConnected event
    if (this.components.size === 0) {
      if (type === 'websocketConnected') {
        this.initializeComponents();
      } else {
        return;
      }
    }

    const sessionId = data.sessionId || this.app.sessions?.getCurrentSessionId();
    if (!sessionId) return;

    let session = Utils.Storage.getSession(sessionId);

    // Update session data based on event type
    switch (type) {
      case 'sessionCreated':
        // Create default session if it doesn't exist
        if (!session && data.sessionId) {
          session = Utils.createDefaultSession(data.sessionId);
        }

        if (!session) return;

        session.id = data.sessionId;
        session.sessionId = data.sessionId;
        session.user.clientId = data.clientId;

        if (this.app.timer.timerCore) {
          session.timer = this.app.timer.timerCore.sync();
        }

        Utils.Storage.saveSession(session.sessionId, session);

        // CRITICAL: Send local data to server to populate newly created empty session
        // The server responds with an empty segments array when it's created, so we need to
        // send the default segments to the server to populate it.
        if (this.app.socket.isSocketConnected()) {
          try {
            // Always send segments to populate server session
            this.app.socket.updateSegments(session.sessionId);

            // Only send timer if it's currently running here
            if (session.timer.isRunning) {
              this.app.socket.updateTimer(session.sessionId);
            }
          } catch (error) {
            console.error('Failed to sync session data to server after sessionCreated:', error);
          }
        }
        break;

      case 'sessionJoined':
        // Create default session if it doesn't exist
        if (!session && data.sessionId) {
          session = Utils.createDefaultSession(sessionId);
        }

        if (!session) return;

        // Overwrite local sessionId with server's sessionId
        session.id = data.sessionId;
        session.sessionId = data.sessionId;
        session.user.clientId = data.clientId;

        if (session.timer.isRunning && !data.session.timer.isRunning) {
          Object.assign(session, {
            ...data.session,
            timer: session.timer.timerCore.sync(),
          });
          Utils.Storage.saveSession(session.sessionId, session);

          // Send timer if it's currently running here but not on the server
          if (this.app.socket.isSocketConnected()) {
            try {
              this.app.socket.updateTimer(data.sessionId);
            } catch (error) {
              console.error('Failed to sync timer state to server after sessionJoined:', error);
            }
          }
        } else {
          // Merge server session and timer state with local state
          Object.assign(session, {
            ...data.session,
            timer: Object.assign(session.timer, data.session.timer),
          });
          Utils.Storage.saveSession(session.sessionId, session);
        }
        break;

      case 'segmentsUpdated':
        // Update segments data from server or local changes
        if (data.session) {
          Object.assign(session, data.session);
          Utils.Storage.saveSession(sessionId, session);
        }
        break;

      case 'timerUpdated':
        // Update timer state from server
        if (data.timer) {
          Object.assign(session.timer, data.timer);
          Utils.Storage.saveSession(sessionId, session);
        }
        break;

      case 'userConnected':
      case 'userDisconnected':
      case 'userUpdated':
        if (!data.user && !session.users[user?.clientId]) return;
        session.users[user.clientId] = data.user;
        Utils.Storage.saveSession(sessionId, session);
        break;

      case 'connectedUsers':
      case 'websocketConnected':
      case 'websocketDisconnected':
        // Doesn't modify session data
        break;

      default:
        console.warn(`Unknown event type for session data update: ${type}`);
    }
  }

  /**
   * Update components sequentially in proper order
   * @param {string} type - WebSocket event type
   * @param {Object} data - Event data
   */
  async updateComponents(type, data) {
    // Define component update order for proper data flow
    const order = ['user', 'sessions', 'timer', 'settings', 'app'];

    // Update component data in defined order and await each update
    for (const name of order) {
      const component = this.components.get(name);
      if (component && component.events.has(type)) {
        try {
          await component.instance?.updateData(type, data);
          await component.instance?.renderHtml(type, data);
        } catch (error) {
          console.error(`Error updating component ${name} for ${type}:`, error);
        }
      }
    }
  }

  /**
   * Dispose coordinator and clean up event listeners
   */
  dispose() {
    this.components.clear();
  }
}

// Initialize coordinator when script loads
const initializeCoordinator = () => {
  Utils.Events.dispatch(document, 'coordinatorReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeCoordinator);
} else {
  initializeCoordinator();
}

window.Coordinator = Coordinator;
