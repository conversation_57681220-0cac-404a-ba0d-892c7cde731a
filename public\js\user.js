/**
 * User manager class
 */
class UserManager {
  constructor(app = null) {
    this.app = app;
    this.currentUser = {
      name: '',
      email: '',
      avatarUrl: '',
      clientId: '',
    };

    this.initialize();
  }

  /**
   * Initialize user manager
   */
  async initialize() {
    this.setupEventListeners();
    await this.initializeUser();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    const elements = getUserElements();
    Utils.Events.on(elements.userProfileBtn, 'click', this.showUserModal.bind(this));

    Utils.Events.on(elements.userNameInput, 'blur', this.saveUserProfile.bind(this));
    Utils.Events.on(elements.userNameInput, 'keypress', (e) => {
      if (e.key === 'Enter') e.target.blur();
    });

    Utils.Events.on(elements.userEmailInput, 'blur', this.saveUserProfile.bind(this));
    Utils.Events.on(elements.userEmailInput, 'keypress', (e) => {
      if (e.key === 'Enter') e.target.blur();
    });
  }

  /**
   * Initialize user data and display
   */
  async initializeUser() {
    await this.loadUserProfile();

    this.renderUserButton();
    this.renderUserModal();
  }

  /**
   * Load user profile for current session
   */
  async loadUserProfile() {
    const session = this.app.sessions?.getCurrentSession();
    if (!session) return;

    if (session.user) {
      this.currentUser.clientId = session.user?.clientId || '';
      this.currentUser.name = session.user?.name || '';
      this.currentUser.email = session.user?.email || '';

      if (session.user?.avatarUrl || session.user?.email) {
        this.currentUser.avatarUrl = await this.getGravatarUrl(session.user?.avatarUrl || session.user?.email);
      } else {
        const hashedId = await Utils.Crypto.hashString(session.user?.clientId);
        this.currentUser.avatarUrl = await this.getGravatarUrl(hashedId);
      }
    }
  }

  /**
   * Save user profile
   */
  async saveUserProfile() {
    const userNameInput = Utils.DOM.getId('user-name-input');
    const userEmailInput = Utils.DOM.getId('user-email-input');

    const newName = userNameInput.value.trim();
    const newEmail = userEmailInput.value.trim();

    const hasChanged = newName !== this.currentUser.name || newEmail !== this.currentUser.email;

    if (hasChanged) {
      const oldEmail = this.currentUser.email;

      this.currentUser.name = newName;
      this.currentUser.email = newEmail;

      if (newEmail !== oldEmail) {
        this.currentUser.avatarUrl = await this.getGravatarUrl(
          newEmail.toLowerCase().trim() || this.currentUser.clientId
        );
      }

      // Update the current user in the local connected users list
      const hashedId = await Utils.Crypto.hashString(this.currentUser.clientId);
      const connectedUsers = this.app.sessions.getConnectedUsers();
      const currentUser = connectedUsers.get(hashedId);

      if (currentUser) {
        currentUser.name = this.currentUser.name;
        currentUser.avatarUrl = this.currentUser.avatarUrl;
        connectedUsers.set(hashedId, currentUser);
      }

      // Update local storage and server
      this.saveToSession();
      this.syncToServer();

      // Notify other components
      Utils.Events.dispatch(document, 'userProfileUpdated', {
        user: this.currentUser,
      });

      // Update display
      this.renderUserButton();
      this.renderUserModal();
    }
  }

  /**
   * Update user data based on event type
   * @param {string} type - WebSocket event type
   * @param {Object} data - Event data
   */
  async updateData(type, data) {
    // Session data is already updated by Coordinator, read from localStorage
    const sessionId = data.sessionId || this.app.sessions?.getCurrentSessionId();
    if (!sessionId) return;

    const session = Utils.Storage.getSession(sessionId);
    if (!sessionId) return;

    const updateAvatar = async (user) => {
      if (user.avatarUrl) {
        return user.avatarUrl;
      } else if (user.email) {
        return await this.getGravatarUrl(user.email);
      } else {
        const hashedId = await Utils.Crypto.hashString(user.clientId);
        return await this.getGravatarUrl(hashedId);
      }
    };

    // Update internal state based on event type
    switch (type) {
      case 'sessionCreated':
        // Update current user with session data
        if (session.user) {
          this.currentUser.clientId = session.user?.clientId || this.currentUser?.clientId;
          this.currentUser.name = session.user?.name || this.currentUser?.name;
          this.currentUser.email = session.user?.email || this.currentUser?.email;
          this.currentUser.avatarUrl = await updateAvatar(this.currentUser);
        }

        this.saveToSession();
        break;

      case 'sessionJoined':
        // Update current user with session data and clientId from server
        if (data.clientId) {
          this.currentUser.clientId = data.clientId;
        }

        // Update current user with session data or fallback to previous data
        if (session.user) {
          this.currentUser.name = session.user?.name || this.currentUser?.name;
          this.currentUser.email = session.user?.email || this.currentUser?.email;
          this.currentUser.avatarUrl = session.user?.avatarUrl || this.currentUser?.avatarUrl;
        }

        this.saveToSession();
        break;
    }
  }

  /**
   * Save current user data to the current session
   */
  saveToSession() {
    const session = this.app.sessions?.getCurrentSession();
    if (!session) return;

    session.user = {
      clientId: this.currentUser.clientId,
      name: this.currentUser.name,
      email: this.currentUser.email,
      avatarUrl: this.currentUser.avatarUrl,
    };

    Utils.Storage.saveSession(session.sessionId, session);
  }

  /**
   * Sync user data with server
   */
  syncToServer() {
    const sessionId = this.app.sessions?.getCurrentSessionId();
    if (!sessionId || !this.app.socket.isSocketConnected()) return;
    this.app.socket.updateUser(sessionId);
  }

  /**
   * Show user profile modal
   */
  async showUserModal() {
    await this.renderUserModal();
    Utils.DOM.showModal('user-modal');
  }

  /**
   * Render user HTML based on current data
   * @param {string} type - WebSocket event type
   * @param {Object} eventDetail - Event data
   */
  async renderHtml() {
    this.renderUserButton();
    this.renderUserModal();
  }

  /**
   * Render user button
   */
  async renderUserButton() {
    const elements = getUserElements();
    elements.userProfileBtn.title = this.getDisplayName();

    if (this.currentUser.avatarUrl || this.currentUser.email) {
      elements.userAvatar.src = await this.getGravatarUrl(this.currentUser.avatarUrl || this.currentUser.email, 80);
    } else {
      const hashedId = await Utils.Crypto.hashString(this.currentUser.clientId);
      elements.userAvatar.src = await this.getGravatarUrl(hashedId, 80);
    }
  }

  /**
   * Populate user modal with current data
   */
  async renderUserModal() {
    const elements = getUserElements();
    elements.userNameInput.value = this.currentUser.name;
    elements.userEmailInput.value = this.currentUser.email;

    if (this.currentUser.avatarUrl || this.currentUser.email) {
      elements.userAvatarLarge.src = await this.getGravatarUrl(this.currentUser.avatarUrl || this.currentUser.email, 160);
    } else {
      const hashedId = await Utils.Crypto.hashString(this.currentUser.clientId);
      elements.userAvatarLarge.src = await this.getGravatarUrl(hashedId, 160);
    }
  }

  /**
   * Get current user data
   * @returns {Object} Current user data
   */
  getCurrentUser() {
    const sessionId = this.app.sessions?.getCurrentSessionId();
    if (!sessionId) return this.currentUser;

    const session = Utils.Storage.getSession(sessionId);
    if (session?.user) Object.assign(this.currentUser, session.user);

    return this.currentUser;
  }

  /**
   * Get user display name
   * @returns {string} Display name or fallback
   */
  getDisplayName() {
    return this.currentUser.name || this.currentUser.email || 'Anonymous';
  }

  /**
   * Generate Gravatar URL using Web Crypto API
   * @param {string} emailOrUrl - User email or base avatar URL
   * @param {number} size - Image size
   * @returns {Promise<string>} Gravatar URL
   */
  async getGravatarUrl(emailOrUrl, size = 80) {
    if (!emailOrUrl) {
      // If no email, use a random string to generate a unique avatar
      const random = Math.random().toString(36).substring(2, 15);
      return this.generateGravatarUrl(random, size);
    }

    if (emailOrUrl.startsWith('https://www.gravatar.com/avatar/')) {
      const baseUrl = emailOrUrl.split('?')[0];
      return `${baseUrl}?s=${size}&d=identicon&r=pg`;
    }

    const email = emailOrUrl.toLowerCase().trim();
    return this.generateGravatarUrl(email, size);
  }

  /**
   * Generate Gravatar URL using Web Crypto API
   * @param {string} email - Preprocessed email
   * @param {number} size - Image size
   * @returns {Promise<string>} Gravatar URL
   */
  async generateGravatarUrl(email, size = 80) {
    const hash = await Utils.Crypto.hashString(email);
    return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=identicon&r=pg`;
  }
}

/**
 * Retrieve all user-related DOM elements
 * @returns {Object} User DOM elements
 */
const getUserElements = () => ({
  userProfileBtn: Utils.DOM.getId('user-profile-btn'),
  userNameInput: Utils.DOM.getId('user-name-input'),
  userEmailInput: Utils.DOM.getId('user-email-input'),
  userAvatar: Utils.DOM.getId('user-avatar'),
  userAvatarLarge: Utils.DOM.getId('user-avatar-large'),
});

const initializeUserManager = () => {
  Utils.Events.dispatch(document, 'userManagerReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeUserManager);
} else {
  initializeUserManager();
}

window.UserManager = UserManager;
