/**
 * Jest setup file for test environment configuration
 */

// Mock Date.now for consistent testing
global.Date.now = jest.fn();

// Set default mock time
const MOCK_TIME = 1672531200000; // 2023-01-01 00:00:00 UTC
global.Date.now.mockReturnValue(MOCK_TIME);

// Helper to advance mock time
global.advanceTime = (milliseconds) => {
  const currentTime = global.Date.now();
  global.Date.now.mockReturnValue(currentTime + milliseconds);
  return currentTime + milliseconds;
};

// Helper to set specific mock time
global.setMockTime = (timestamp) => {
  global.Date.now.mockReturnValue(timestamp);
  return timestamp;
};

// Reset time to default before each test
beforeEach(() => {
  global.Date.now.mockReturnValue(MOCK_TIME);
});
