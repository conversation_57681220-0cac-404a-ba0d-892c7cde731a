// Playwright configuration for headed mode
// This config will run tests in headed (non-headless) mode for visual debugging

/** @type {import('@playwright/test').PlaywrightTestConfig} */
const config = {
  use: {
    headless: false,
    viewport: { width: 1280, height: 800 },
    video: 'retain-on-failure',
    screenshot: 'only-on-failure',
    launchOptions: {
      slowMo: 100, // slow down actions for easier observation
    },
  },
  testDir: './tests/playwright',
  timeout: 60000,
  outputDir: './tests/playwright/results',
};

// eslint-disable-next-line no-undef
module.exports = config;
