---
applyTo: '**'
---

# Testing Guidelines

Testing guidelines for coding agents to follow when writing tests.

## Testing

- ONLY write tests when EXPLICITLY asked to do so.
- DO NOT create mocks for existing code.
- ONLY create mocks if absolutely necessary.
- ALWAYS import the actual code to test.
- ALWAYS import from the actual file.
- DO NOT clone or copy code from the actual file to the test file.
- Each test file must include a brief description at the top explaining its purpose.
- Use clear and consistent naming conventions for test files and test cases.
- Ensure test data is cleaned up after tests run.
- Strive for comprehensive test coverage (unit, integration, e2e as appropriate).
