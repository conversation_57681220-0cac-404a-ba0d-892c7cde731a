---
applyTo: '**/*.{ts,js}'
---

# JavaScript Guidelines

JavaScript and TypeScript specific guidelines for coding agents to follow.

## Coding

- Use ES6+ syntax for JavaScript and TypeScript.
- Use ES module import/export syntax consistently.
- Use template literals for string interpolation.
- Use arrow functions for concise function expressions.
- Use `const` and `let` for variable declarations, avoiding `var`.
- Use strict equality (`===`) for comparisons.
- Use async/await for asynchronous operations.
- Use error handling with try/catch blocks for asynchronous operations.
- Organize code into modules/files by feature or responsibility.
- Use the configured linter and fix all lint errors before submitting code.
- All functions and classes must have JSDoc documentation with full parameter and return types.
- Use JSDoc for function documentation to ensure clarity on parameters and return types.
  - Keep comments to a minimum
  - Only use comments in code to explain complex logic or non-obvious code.

# Linting

- DO NOT add eslint-disable-next-line * comments UNLESS EXPLICTLY ASKED TO DO SO.
- DO NOT add comments to disable lint errors UNLESS EXPLICTLY ASKED TO DO SO.
- DO NOT add comments to ignore lint errors UNLESS EXPLICTLY ASKED TO DO SO.

## Examples

JSDoc examples. These ONLY apply to JAVASCRIPT FILES. TypeScript files use TypeDoc, see the TypeScript instructions for more details.

### Function Documentation

```javascript
/**
 * Calculate tax
 * @param {number} amount - Total amount
 * @param {number} tax - Tax percentage
 * @returns {string} - Total with a dollar sign
 */
const calculateTax = (amount, tax) => {
  return `$${amount + tax * amount}`;
};
```

```javascript
/**
 * Prints a string to the console.
 * @param x the string to print.
 */
export function print(x: string): void {
    console.log(x);
}

/**
 * Prints 'Hello World!' to the console.
 */
export function hello(): void {
    console.log('Hello World!');
}
```