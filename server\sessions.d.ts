/**
 * Type definitions for sessions.ts (Session management)
 */

import WebSocket from 'ws';
import type { IncomingMessage, SessionInternal } from './formatters.d';

export interface ServerWebSocket extends WebSocket {
  isAlive?: boolean;
  sessionId?: string | null;
  clientId?: string | null;
}

declare class SessionManager {
  getSession(sessionId: string): SessionInternal | null;
  handleMessage(ws: ServerWebSocket, data: IncomingMessage): void;
  removeClient(sessionId: string, clientId: string): void;
  dispose(): void;
}

export default SessionManager;
