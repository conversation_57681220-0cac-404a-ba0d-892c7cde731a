export const testEnvironment = 'jsdom';
export const setupFilesAfterEnv = ['<rootDir>/tests/setup.js'];
export const collectCoverageFrom = ['public/js/**/*.js', '!public/js/libs/**'];
export const coverageThreshold = {
  global: {
    statements: 80,
    branches: 80,
    functions: 80,
    lines: 80,
  },
  './public/js/timer.js': {
    statements: 95,
    branches: 95,
    functions: 95,
    lines: 95,
  },
};
