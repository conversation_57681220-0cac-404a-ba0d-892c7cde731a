---
applyTo: '**'
---

# Windows Guidelines

Instructions for coding agents to follow when running tasks on Windows machines.

## Command Line Interface

- ALWAYS use PowerShell commands.
- DO NOT use Linux commands directly unless they are valid PowerShell aliases.
- DO NOT use MacOS commands.
- Use absolute paths or the correct path separator for Windows (`\`).
- When executing commands, always prefer the full PowerShell cmdlet name for clarity and cross-platform compatibility.
- Some Linux commands (e.g., `ls`, `cat`, `pwd`, `cp`, `mv`, `rm`, `touch`) are available as aliases in PowerShell, but their behavior may differ. Prefer the full cmdlet name.

### Common PowerShell Commands (with Linux Equivalents)

| Task                       | PowerShell Command                                | Linux Equivalent          |
| -------------------------- | ------------------------------------------------- | ------------------------- |
| List files                 | `Get-ChildItem` or `ls`                           | `ls`                      |
| List directories           | `Get-ChildItem -Directory`                        | `ls -d */`                |
| Change directory           | `Set-Location` or `cd`                            | `cd`                      |
| Print working directory    | `Get-Location` or `pwd`                           | `pwd`                     |
| Remove file                | `Remove-Item file.txt` or `rm file.txt`           | `rm file.txt`             |
| Remove directory           | `Remove-Item dir -Recurse -Force`                 | `rm -rf dir`              |
| Copy file                  | `Copy-Item src.txt dest.txt` or `cp`              | `cp`                      |
| Move file                  | `Move-Item src.txt dest.txt` or `mv`              | `mv`                      |
| Create directory           | `New-Item -ItemType Directory -Path dir`          | `mkdir dir`               |
| Read file                  | `Get-Content file.txt` or `cat file.txt`          | `cat file.txt`            |
| Write file                 | `Set-Content file.txt 'text'`                     | `echo 'text' > file.txt`  |
| Append file                | `Add-Content file.txt 'text'`                     | `echo 'text' >> file.txt` |
| Find text in file          | `Select-String 'pattern' file.txt`                | `grep 'pattern' file.txt` |
| Search files recursively   | `Get-ChildItem -Recurse -Filter *.txt`            | `find . -name '*.txt'`    |
| Show command history       | `Get-History` or `history`                        | `history`                 |
| Run previous command       | `Invoke-History` or `r`                           | `!!`                      |
| Show running processes     | `Get-Process` or `ps`                             | `ps`                      |
| Kill process               | `Stop-Process -Id 1234` or `kill 1234`            | `kill 1234`               |
| Show environment variables | `Get-ChildItem Env:`                              | `printenv`                |
| Show command help          | `Get-Help Get-ChildItem` or `man`                 | `man`                     |
| Download file              | `Invoke-WebRequest -Uri ... -OutFile ...`         | `wget`/`curl`             |
| SSH to server              | `ssh user@host`                                   | `ssh user@host`           |
| Compress files (zip)       | `Compress-Archive -Path ... -DestinationPath ...` | `zip`                     |
| Extract zip                | `Expand-Archive -Path ... -DestinationPath ...`   | `unzip`                   |

### Example PowerShell Commands

- Remove File: `Remove-Item -Path .\src\file.txt -Force`
- Remove Directory: `Remove-Item -Path .\src\dir -Recurse -Force`
- Create Directory: `New-Item -Path .\src\dir -ItemType Directory`
- Copy File: `Copy-Item -Path .\src\file.txt -Destination .\build\file.txt`
- Move File: `Move-Item -Path .\src\file.txt -Destination .\build\file.txt`
- Rename File: `Rename-Item -Path .\src\file.txt -NewName file2.txt`
- Read File: `Get-Content -Path .\src\file.txt`
- Write File: `Set-Content -Path .\src\file.txt -Value "Hello World"`
- Append File: `Add-Content -Path .\src\file.txt -Value "Hello World"`
- Check if File Exists: `Test-Path -Path .\src\file.txt`
- Check if Directory Exists: `Test-Path -Path .\src\dir -PathType Container`
- Get Current Directory: `Get-Location`
- Change Directory: `Set-Location -Path .\src`
- List Files: `Get-ChildItem -Path .\src`
- List Directories: `Get-ChildItem -Path .\src -Directory`
- List Files and Directories: `Get-ChildItem -Path .\src -Recurse`
- Download file: `Invoke-WebRequest -Uri https://example.com/file.txt -OutFile .\src\file.txt`
- Make API request: `Invoke-RestMethod -Uri https://example.com/api -Method GET`
- Search for files: `Get-ChildItem -Path .\src -Recurse -Filter *.txt`
- Search for files and directories with specific name: `Get-ChildItem -Path .\src -Recurse -Filter *.txt -Include *.txt, *.exe -Name file.txt`
- Show command help: `Get-Help Get-ChildItem`
- Show command syntax: `Get-Command Get-ChildItem -Syntax`
- Show all commands: `Get-Command *`
- Show all processes: `Get-Process`
- Kill process: `Stop-Process -Id 1234`
- Show environment variables: `Get-ChildItem Env:`
- Show command history: `Get-History`
- Run previous command: `Invoke-History`

### DO NOT USE THESE COMMANDS (Not available in Windows/PowerShell)

- `sudo` (use `Start-Process -Verb RunAs` for elevation)
- `apt`, `yum`, `dnf`, `pacman` (use Windows package managers like `winget`, `choco`)
- `systemctl`, `service` (use Windows Services tools)
- `ifconfig`, `ip` (use `Get-NetIPAddress`, `Get-NetIPConfiguration`)
- `journalctl`, `dmesg` (use Windows Event Viewer or `Get-EventLog`)
- `mount`, `umount` (use `Mount-DiskImage`, `Dismount-DiskImage`)
- `chmod`, `chown` (use `icacls`, `Set-Acl`)
- `tar` (use `Compress-Archive`, `Expand-Archive`)
- `scp` (use `Copy-Item -ToSession` for remote copy)
- `rsync` (no direct equivalent, use `robocopy`)
- `less`, `more` (use `Out-Host`, `Out-GridView`)
- `man` (use `Get-Help`)

> Note: Some Linux commands (e.g., `ls`, `cat`, `pwd`, `cp`, `mv`, `rm`, `touch`) are available as aliases in PowerShell, but always prefer the full cmdlet name for clarity and compatibility.
