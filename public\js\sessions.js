/**
 * Session manager class
 */
class SessionManager {
  constructor(app = null) {
    this.app = app;
    this.currentSessionId = null;
    this.connectedUsers = new Map();

    this.initialize();
  }

  /**
   * Initialize session manager
   */
  initialize() {
    this.setupEventListeners();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    const elements = getSessionElements();

    Utils.Events.on(elements.sessionsBtn, 'click', this.showSessionsList.bind(this));
    Utils.Events.on(elements.connectedUsers, 'click', this.showConnectedUsersPopup.bind(this));
    Utils.Events.on(elements.statusBar, 'click', this.handleStatusBarClick.bind(this));

    // Handle popup close buttons
    const popupCloseBtns = Utils.DOM.queryAll('.close-popup-btn');
    popupCloseBtns.forEach((btn) => {
      Utils.Events.on(btn, 'click', (e) => {
        const popup = e.target.closest('.popup');
        if (popup) Utils.DOM.hidePopup(popup.id);
      });
    });

    // Handle outside clicks for popups
    Utils.Events.on(document, 'click', (e) => {
      const usersPopup = elements.usersPopup;
      const sessionsPopup = elements.sessionsPopup;
      const connectedUsersBtn = elements.connectedUsers;
      const sessionsBtn = elements.sessionsBtn;

      Utils.Events.on(elements.clearAllBtn, 'click', this.clearSessions.bind(this));

      // Close users popup if clicking outside
      if (
        usersPopup &&
        !usersPopup.classList.contains('hidden') &&
        !usersPopup.contains(e.target) &&
        e.target !== connectedUsersBtn &&
        !connectedUsersBtn?.contains(e.target)
      ) {
        Utils.DOM.hidePopup('users-popup');
      }

      // Close sessions popup if clicking outside
      if (
        sessionsPopup &&
        !sessionsPopup.classList.contains('hidden') &&
        !sessionsPopup.contains(e.target) &&
        e.target !== sessionsBtn &&
        !sessionsBtn?.contains(e.target)
      ) {
        Utils.DOM.hidePopup('sessions-popup');
      }
    });

    // Handle modal close buttons
    Utils.Events.on(document, 'click', (e) => {
      if (e.target.classList.contains('close')) {
        const modal = e.target.closest('.modal');
        if (modal) Utils.DOM.hideModal(modal.id);
        return;
      }

      if (e.target.classList.contains('modal')) {
        Utils.DOM.hideModal(e.target.id);
        return;
      }
    });

    // WebSocket events
    Utils.Events.on(document, 'websocketConnected', this.initializeFromUrl.bind(this));
  }

  /**
   * Initialize session from URL
   */
  initializeFromUrl() {
    const sessionId = Utils.getSessionIdFromUrl();

    if (sessionId) {
      this.joinSession(sessionId);
    } else {
      // No session in URL, create a new one
      this.createSession();
    }
  }

  /**
   * Create a new session
   */
  createSession() {
    const sessionId = Utils.generateSessionId();
    this.joinSession(sessionId);
  }

  /**
   * Join an existing session or create it if it doesn't exist
   * @param {string} sessionId - Session ID to join
   */
  joinSession(sessionId) {
    if (!Utils.isValidSessionId(sessionId)) {
      console.error('Invalid session ID:', sessionId);
      this.createSession();
      return;
    }

    this.currentSessionId = sessionId;
    Utils.setSessionIdInUrl(sessionId);

    // Join session via WebSocket when connected
    if (this.app.socket.isSocketConnected()) {
      const currentUser = this.app.user?.getCurrentUser();
      this.app.socket.joinSession(sessionId, currentUser);
    }
  }

  /**
   * Update component data based on websocket events
   * @param {string} type - WebSocket event type
   * @param {Object} data - Event data
   */
  async updateData(type, data) {
    const sessionId = data.sessionId || this.currentSessionId;
    if (!sessionId) return;

    const currentUser = this.app.user.getCurrentUser();

    const addCurrentUserToList = async () => {
      if (currentUser?.clientId) {
        const hashedId = await Utils.Crypto.hashString(currentUser.clientId);
        this.connectedUsers.set(hashedId, {
          clientId: hashedId,
          name: currentUser.name,
          avatarUrl: await this.app.user.getGravatarUrl(currentUser.avatarUrl || currentUser.email || hashedId),
          isOnline: true,
        });
      }
    };

    // Update internal state based on event type
    switch (type) {
      case 'sessionCreated':
        this.currentSessionId = sessionId;
        this.connectedUsers.clear();
        await addCurrentUserToList();
        break;

      case 'sessionJoined':
        // TODO: If currentSessionId is different, start a new session
        this.currentSessionId = sessionId;
        this.connectedUsers.clear();

        const session = data.session;
        if (session && session.users && Object.keys(session.users).length > 0) {
          // TODO: Looping over the users map and copying to this.connectedUsers is unnecessary
          //       Change this.connectedUsers from a map to an object so it can be directly replaced
          Object.values(session.users).forEach((user) => {
            this.connectedUsers.set(user.clientId, user);
          });
        } else {
          await addCurrentUserToList();
        }
        break;

      case 'userConnected':
        this.connectedUsers.set(data.user.clientId, data.user);
        break;

      case 'userDisconnected':
        this.connectedUsers.delete(data.user.clientId);
        break;

      case 'userUpdated':
        this.connectedUsers.set(data.user.clientId, data.user);
        break;

      case 'connectedUsers':
        this.connectedUsers.clear();
        data.users.forEach((user) => {
          this.connectedUsers.set(user.clientId, user);
        });
        break;
    }
  }

  /**
   * Render sessions HTML based on current data
   */
  async renderHtml() {
    this.renderDescription();
    this.renderUsersCount();
    this.renderConnectedUsersList();
  }

  /**
   * Update session header information
   */
  renderDescription() {
    const session = Utils.Storage.getSession(this.currentSessionId);
    const elements = getSessionElements();
    elements.sessionName.textContent = session?.name || '';
    elements.sessionDesc.textContent = session?.description || '';
  }

  /**
   * Show sessions list modal
   */
  showSessionsList() {
    const elements = getSessionElements();
    if (elements.sessionsPopup) {
      Utils.DOM.showModal('sessions-popup');
      this.renderSessionsList();
    }
  }

  /**
   * Render sessions list in modal
   */
  renderSessionsList() {
    const elements = getSessionElements();
    if (elements.sessionsList) {
      const clearAllBtn = elements.clearAllBtn;

      const sessionIds = Utils.Storage.getAllSessionIds();
      if (sessionIds.length === 0) {
        elements.sessionsList.innerHTML = '<p class="empty-state">No sessions found</p>';
        clearAllBtn.style.display = 'none';
        return;
      }

      clearAllBtn.style.display = 'block';

      elements.sessionsList.innerHTML = '';

      sessionIds.forEach((sessionId) => {
        const session = Utils.Storage.getSession(sessionId);
        if (!session) return;

        const sessionItem = this.renderSessionItem(sessionId, session);
        elements.sessionsList.appendChild(sessionItem);
      });

      this.renderClearButtonText();
    }
  }

  /**
   * Create session list item element
   * @param {string} sessionId - Session ID
   * @param {Object} session - Session data
   * @returns {HTMLElement} Session item element
   */
  renderSessionItem(sessionId, session) {
    const item = Utils.DOM.create('div', {
      className: `session-item ${sessionId === this.currentSessionId ? 'current' : ''}`,
      'data-session-id': sessionId,
    });

    const sessionInfo = Utils.DOM.create('div', { className: 'session-item-info' });

    const sessionName = Utils.DOM.create(
      'div',
      {
        className: 'session-item-name',
      },
      session.name || sessionId
    );

    const sessionIdSpan = Utils.DOM.create(
      'div',
      {
        className: 'session-item-id',
      },
      sessionId
    );

    const sessionDescription = Utils.DOM.create(
      'div',
      {
        className: 'session-item-description',
      },
      session.description
    );

    const checkbox = Utils.DOM.create('input', {
      type: 'checkbox',
      className: 'session-checkbox',
    });

    const deleteBtn = Utils.DOM.create(
      'button',
      {
        className: 'session-delete-btn btn-danger btn-small',
      },
      '✖'
    );

    const sessionDetails = Utils.DOM.create('div', { className: 'session-item-details' });
    const sessionActions = Utils.DOM.create('div', { className: 'session-item-actions' });

    sessionInfo.appendChild(sessionName);
    sessionInfo.appendChild(sessionDetails);

    sessionDetails.appendChild(sessionDescription);
    if (session.name) sessionDetails.appendChild(sessionIdSpan);

    sessionActions.appendChild(checkbox);
    sessionActions.appendChild(deleteBtn);

    item.appendChild(sessionInfo);
    item.appendChild(sessionActions);

    Utils.Events.on(sessionInfo, 'click', () => {
      if (sessionId !== this.currentSessionId) {
        const url = `${window.location.origin}/${sessionId}`;
        window.open(url, '_blank');
      }
    });

    Utils.Events.on(checkbox, 'change', this.renderClearButtonText.bind(this));
    Utils.Events.on(deleteBtn, 'click', (e) => {
      e.stopPropagation();
      this.deleteSession(sessionId);
    });

    return item;
  }

  /**
   * Update clear button text based on selected items
   */
  renderClearButtonText() {
    const elements = getSessionElements();
    if (elements.clearAllBtn) {
      const clearAllBtn = elements.clearAllBtn;

      const checkboxes = Utils.DOM.queryAll('.session-checkbox:checked');
      if (checkboxes.length > 0) {
        clearAllBtn.textContent = `Delete Selected Sessions (${checkboxes.length})`;
      } else {
        clearAllBtn.textContent = 'Clear All Sessions';
      }
    }
  }

  /**
   * Clear all sessions
   */
  async clearSessions() {
    const checkboxes = Utils.DOM.queryAll('.session-checkbox:checked');
    if (checkboxes.length > 0) return this.deleteSelected();

    const confirmed = await Utils.showConfirm('Delete all sessions? This cannot be undone.');
    if (confirmed) {
      Utils.Storage.clearAllSessions();

      this.connectedUsers.clear();
      this.clearCurrentUser();

      this.createSession();

      this.renderUsersCount();
      this.renderSessionsList();
    }
  }

  /**
   * Delete selected sessions
   */
  async deleteSelected() {
    const checkboxes = Utils.DOM.queryAll('.session-checkbox:checked');
    const sessionIds = Array.from(checkboxes)
      .map((cb) => cb.closest('.session-item').getAttribute('data-session-id'))
      .filter((id) => id !== null); // Filter out any null values

    if (sessionIds.length === 0) return;

    const confirmed = await Utils.showConfirm(`Delete ${sessionIds.length} selected session(s)?`);
    if (confirmed) {
      let isCurrent = false;

      sessionIds.forEach((sessionId) => {
        Utils.Storage.deleteSession(sessionId);
        if (!isCurrent) {
          isCurrent = sessionId === this.currentSessionId;
        }
      });

      if (isCurrent) {
        this.connectedUsers.clear();
        this.clearCurrentUser();

        this.createSession();

        this.renderUsersCount();
      }

      this.renderSessionsList();
    }
  }

  /**
   * Delete a specific session
   * @param {string} sessionId - Session ID to delete
   */
  async deleteSession(sessionId) {
    const confirmed = await Utils.showConfirm(`Delete session "${sessionId}"?`);
    if (confirmed) {
      Utils.Storage.deleteSession(sessionId);

      if (sessionId === this.currentSessionId) {
        this.connectedUsers.clear();
        this.clearCurrentUser();

        this.createSession();

        this.renderUsersCount();
      }

      this.renderSessionsList();
    }
  }

  /**
   * Clear current user data
   */
  clearCurrentUser() {
    this.app.user.currentUser.clientId = '';
    this.app.user.currentUser.name = '';
    this.app.user.currentUser.email = '';
    this.app.user.currentUser.avatarUrl = '';
    this.app.user.renderUserButton();
  }

  /**
   * Show connected users popup
   */
  showConnectedUsersPopup() {
    const elements = getSessionElements();
    if (elements.usersPopup) {
      Utils.DOM.showModal('users-popup');
      this.renderConnectedUsersList();
    }
  }

  /**
   * Render connected users list in modal
   */
  async renderConnectedUsersList() {
    const elements = getSessionElements();
    elements.usersList.innerHTML = '';
    if (this.connectedUsers.size === 0) {
      elements.usersList.innerHTML = '<p class="empty-state">No users currently connected</p>';
      return;
    }
    const userItems = await Promise.all(
      Array.from(this.connectedUsers.values()).map((user) => this.renderConnectedUserItem(user))
    );
    userItems.forEach((item) => elements.usersList.appendChild(item));
  }

  /**
   * Render connected user item element
   * @param {Object} user - User object
   * @returns {Promise<HTMLElement>} User item element
   */
  async renderConnectedUserItem(user) {
    const item = Utils.DOM.create('div', { className: 'user-item' });
    const avatar = Utils.DOM.create('div', { className: 'user-item-avatar' });
    const info = Utils.DOM.create('div', { className: 'user-item-info' });

    // Determine online/offline status based on isOnline
    const statusClass = user.isOnline ? 'online' : 'offline';

    const image = Utils.DOM.create('img', {
      src: await this.app.user.getGravatarUrl(user.avatarUrl || user.clientId, 40),
      alt: user.name || 'User avatar',
      className: `user-status-${statusClass}`,
    });

    const name = Utils.DOM.create(
      'div',
      {
        className: 'user-item-name',
      },
      user.name || 'Anonymous User'
    );

    avatar.appendChild(image);
    info.appendChild(name);

    item.appendChild(avatar);
    item.appendChild(info);

    return item;
  }

  /**
   * Render connected users count
   */
  renderUsersCount() {
    const elements = getSessionElements();
    const count = this.connectedUsers.size;
    elements.connectedUsers.textContent = `${count} user${count === 1 ? '' : 's'} connected`;
  }

  /**
   * Get current session ID
   * @returns {string|null} Current session ID
   */
  getCurrentSessionId() {
    return this.currentSessionId;
  }

  /**
   * Get current session data
   * @returns {Object|null} Current session data
   */
  getCurrentSession() {
    return this.currentSessionId ? Utils.Storage.getSession(this.currentSessionId) : null;
  }

  /**
   * Get connected users
   * @returns {Map} Map of connected users
   */
  getConnectedUsers() {
    return this.connectedUsers;
  }

  /**
   * Handle status bar click to request connected users from server
   */
  handleStatusBarClick() {
    const sessionId = this.getCurrentSessionId();
    if (sessionId && this.app.socket.isSocketConnected()) {
      this.app.socket.getConnectedUsers();
    }
  }

  /**
   * Export session configuration
   * @param {string} sessionId - Session ID to export
   * @returns {Object} Session configuration
   */
  exportSessionConfig(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    if (!session) return null;

    return {
      name: session.name,
      description: session.description,
      segments: session.segments.items.map((segment) => ({
        name: segment.name,
        duration: segment.duration,
        alert: segment.alert,
        customCSS: segment.customCSS,
      })),
    };
  }

  /**
   * Import session configuration
   * @param {Object} config - Session configuration
   * @param {string} sessionId - Target session ID
   */
  importSessionConfig(config, sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    if (!session) return;

    if (config.name) session.name = config.name; // TODO: Validate name
    if (config.description) session.description = config.description; // TODO: Validate description

    if (config.segments && Array.isArray(config.segments)) {
      session.segments.items = config.segments.map((segment) => ({
        name: segment?.name || '', // TODO: Validate name
        duration: parseInt(segment?.duration || 0) || 1500, // TODO: Validate duration
        alert: segment?.alert || 'Default', // TODO: Validate against available alerts
        customCSS: segment?.customCSS || '', // TODO: Sanitize CSS
      }));
      session.segments.lastUpdated = Utils.getCurrentTimestamp();
    }

    // Reset timer to first segment
    session.timer.currentSegment = 0;
    session.timer.timeRemaining = (session.segments.items[0]?.duration || 1500) * 1000; // Convert to milliseconds
    session.timer.isRunning = false;
    session.timer.isPaused = false;
    session.timer.startedAt = 0;
    session.timer.startedSegment = 0;
    session.timer.pausedAt = 0;
    session.timer.timePaused = 0;

    // Save to localStorage first (optimistic update)
    Utils.Storage.saveSession(sessionId, session);

    // Sync with server after localStorage save
    if (this.app.socket.isSocketConnected()) {
      // TODO: Combine into single message
      this.app.socket.updateSegments(sessionId);
      this.app.socket.updateTimer(sessionId);
    }

    // Make segments immediately available by dispatching update event
    Utils.Events.dispatch(document, 'segmentsUpdated', {
      sessionId: sessionId,
      name: session.name,
      description: session.description,
      segments: session.segments,
      source: 'local',
    });

    // Update UI
    this.renderDescription(sessionId);

    // Reload timer
    this.app.timer.loadSession(sessionId);
  }
}

/**
 * Retrieve all session-related DOM elements
 * @returns {Object} Session DOM elements
 */
const getSessionElements = () => ({
  sessionsBtn: Utils.DOM.getId('sessions-btn'),
  connectedUsers: Utils.DOM.getId('connected-users'),
  statusBar: Utils.DOM.query('.status-bar'),
  sessionsPopup: Utils.DOM.getId('sessions-popup'),
  usersPopup: Utils.DOM.getId('users-popup'),
  clearAllBtn: Utils.DOM.getId('clear-all-sessions-btn'),
  sessionsList: Utils.DOM.getId('sessions-list'),
  sessionName: Utils.DOM.getId('session-name'),
  sessionDesc: Utils.DOM.getId('session-description'),
  usersList: Utils.DOM.getId('users-list'),
});

const initializeSessionManager = () => {
  Utils.Events.dispatch(document, 'sessionManagerReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeSessionManager);
} else {
  initializeSessionManager();
}

window.SessionManager = SessionManager;
