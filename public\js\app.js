/**
 * Timer application class
 */
class TimerApp {
  constructor() {
    // Application state
    this.isInitialized = false;
    this.isDisconnected = false;

    // Components
    this.alerts = null;
    this.coordinator = null;
    this.sessions = null;
    this.settings = null;
    this.share = null;
    this.socket = null;
    this.timer = null;
    this.user = null;

    // Initialize app
    this.initialize();
  }

  /**
   * Initialize the application
   */
  initialize() {
    if (this.isInitialized) return;

    console.log('Initializing Timer App...');

    if (document.readyState === 'loading') {
      console.log('Waiting for DOMContentLoaded to initialize app...');
      Utils.Events.on(document, 'DOMContentLoaded', this.initializeApp.bind(this));
    } else {
      console.log('DOMContentLoaded already fired, initializing app...');
      this.initializeApp();
    }
  }

  /**
   * Initialize application components
   */
  initializeApp() {
    try {
      this.initializeComponents();
      this.setupEventHandlers();
      this.setupKeyboardShortcuts();
      this.setupPageVisibility();
      this.connectSocket();

      this.isInitialized = true;
      console.log('Timer App initialized successfully');

      Utils.Events.dispatch(document, 'appReady');
    } catch (error) {
      console.error('Failed to initialize app:', error);
    }
  }

  /**
   * Initialize application components
   */
  initializeComponents() {
    this.alerts = new AlertSystem(this);
    this.coordinator = new Coordinator(this);
    this.sessions = new SessionManager(this);
    this.settings = new SettingsManager(this);
    this.share = new ShareManager(this);
    this.socket = new WebSocketClient(this);
    this.timer = new Timer(this);
    this.user = new UserManager(this);
  }

  /**
   * Setup global event handlers
   */
  setupEventHandlers() {
    // Handle beforeunload to save state (triggered when reloading or closing the page)
    Utils.Events.on(window, 'beforeunload', this.handleBeforeUnload.bind(this));

    // Handle focus/blur for pause/resume
    Utils.Events.on(window, 'focus', this.handleWindowFocus.bind(this));

    // Click status element to reconnect when disconnected
    const statusEl = Utils.DOM.getId('connection-status');
    Utils.Events.on(statusEl, 'click', () => {
      if (this.isDisconnected) this.connectSocket();
    });
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    Utils.Events.on(document, 'keydown', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      // Handle shortcuts
      switch (event.key) {
        case ' ': // Spacebar - Start/Pause timer
          event.preventDefault();
          this.toggleTimer();
          break;

        case 'Escape': // Escape - Close modals
          this.closeAllModals();
          break;

        case 's': // S - Show settings
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.settings?.showSettings();
          }
          break;

        case 'u': // U - Show user profile
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.user?.showUserModal();
          }
          break;

        case 'l': // L - Show sessions list
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.sessions?.showSessionsList();
          }
          break;
      }
    });
  }

  /**
   * Setup page visibility handling
   */
  setupPageVisibility() {
    let eventName = 'visibilitychange';

    // Handle vendor prefixes
    if (typeof document.hidden === 'undefined') {
      if (typeof document.webkitHidden !== 'undefined') {
        eventName = 'webkitvisibilitychange';
      } else if (typeof document.mozHidden !== 'undefined') {
        eventName = 'mozvisibilitychange';
      } else if (typeof document.msHidden !== 'undefined') {
        eventName = 'msvisibilitychange';
      }
    }

    Utils.Events.on(document, eventName, () => {
      if (!document.hidden) {
        this.handlePageVisible();
      }
    });
  }

  /**
   * Toggle timer start/pause
   */
  toggleTimer() {
    if (!this.timer) return;

    const state = this.timer.getTimerState();

    if (!state.isRunning || state.isPaused) {
      this.timer.start();
    } else {
      this.timer.pause();
    }
  }

  /**
   * Connect to WebSocket server
   */
  connectSocket() {
    if (this.socket) {
      this.socket.connect();
    }
  }

  /**
   * Close all open modals
   */
  closeAllModals() {
    const modals = Utils.DOM.queryAll('.modal.show');
    modals.forEach((modal) => {
      Utils.DOM.hideModal(modal.id);
    });
  }

  /**
   * Handle page focus
   *
   * Unfreze timer display if it froze
   * while the page was unfocused
   */
  handleWindowFocus() {
    if (this.timer) {
      this.timer.tickTimer();
    }
  }

  /**
   * Handle page visible
   *
   * Reconnect WebSocket if needed
   */
  handlePageVisible() {
    if (this.socket && !this.socket.isSocketConnected()) {
      this.socket.connect();
    }
  }

  /**
   * Handle before unload
   */
  handleBeforeUnload() {
    this.disposeComponents();
  }

  /**
   * Dispose of all components that have a dispose method
   */
  disposeComponents() {
    Object.entries(this).forEach(([name, component]) => {
      if (component && typeof component.dispose === 'function') {
        try {
          console.log(`Disposing component: ${name}`);
          component.dispose();
        } catch (error) {
          console.warn(`Error disposing component ${name}:`, error);
        }
      }
    });
  }

  /**
   * Update data based on WebSocket events (coordinator interface)
   * @param {string} type - The WebSocket event name
   */
  updateData(type) {
    switch (type) {
      case 'websocketConnected':
        if (this.isDisconnected) {
          this.isDisconnected = false;
        }
        break;

      case 'websocketDisconnected':
        this.isDisconnected = true;
        break;
    }
  }

  /**
   * Render HTML based on updated data (coordinator interface)
   * @param {string} type - The WebSocket event name
   */
  renderHtml(type) {
    switch (type) {
      case 'websocketConnected':
        console.log('WebSocket connected');
        break;

      case 'websocketDisconnected':
        console.log('WebSocket disconnected');
        break;
    }
  }
}

// Application instance
let app;

// Ensure all components are loaded before initializing app
const readyStates = new Set();
const readyEvents = {
  alertsReady: {
    name: 'alertSystemReady',
    check: () => window.AlertSystem,
    listen: null,
  },
  coordinatorReady: {
    name: 'coordinatorReady',
    check: () => window.Coordinator,
    listen: null,
  },
  sessionsReady: {
    name: 'sessionManagerReady',
    check: () => window.SessionManager,
    listen: null,
  },
  settingsReady: {
    name: 'settingsManagerReady',
    check: () => window.SettingsManager,
    listen: null,
  },
  shareReady: {
    name: 'shareManagerReady',
    check: () => window.ShareManager,
    listen: null,
  },
  timerReady: {
    name: 'timerReady',
    check: () => window.Timer,
    listen: null,
  },
  userReady: {
    name: 'userManagerReady',
    check: () => window.UserManager,
    listen: null,
  },
  utilsReady: {
    name: 'utilsReady',
    check: () => window.Utils,
    listener: null,
  },
  webSocketClientReady: {
    name: 'webSocketClientReady',
    check: () => window.WebSocketClient,
    listen: null,
  },
};

const startApp = () => {
  // Initialize app if all components are loaded
  if (readyStates.size === Object.keys(readyEvents).length && !app) {
    console.log('All components loaded, initializing app...');

    readyStates.clear();
    Object.values(readyEvents).forEach((event) => {
      if (event.listen) {
        document.removeEventListener(event.name, event.listen);
      }
    });

    app = new TimerApp();
    window.app = app;
  }
};

// Check for already available components
Object.values(readyEvents).forEach((event) => {
  if (event.check()) {
    readyStates.add(event.name);
  } else {
    // Add event listener to wait for component to load
    document.addEventListener(event.name, () => {
      readyStates.add(event.name);
      event.listen = this;
      startApp();
    });
  }
});

startApp();

window.TimerApp = TimerApp;
