/**
 * Tests for Timer class
 * Testing the actual Timer class methods without mocks
 */

const Timer = require('../../shared/timer-core');

describe('Timer', () => {
  let timer;
  const mockSegments = [
    { name: 'Work', duration: 25, alert: 'Default', customCSS: '' },
    { name: 'Break', duration: 5, alert: 'Default', customCSS: '' },
    { name: 'Long Break', duration: 15, alert: 'Default', customCSS: '' },
  ];

  beforeEach(() => {
    timer = new Timer(mockSegments);
  });

  describe('constructor', () => {
    it('should initialize with segments and default state', () => {
      const state = timer.getState();
      expect(state.repeat).toBe(false);
      expect(state.currentSegment).toBe(0);
      expect(state.timeRemaining).toBe(25000); // 25 seconds in milliseconds
      expect(state.isRunning).toBe(false);
      expect(state.isPaused).toBe(false);
      expect(state.startedAt).toBe(0);
      expect(state.startedSegment).toBe(0);
      expect(state.pausedAt).toBe(0);
      expect(state.timePaused).toBe(0);
    });

    it('should store the passed segments correctly', () => {
      expect(timer.segments).toEqual(mockSegments);
    });

    it('should initialize with empty segments array', () => {
      const emptyTimer = new Timer([]);
      const state = emptyTimer.getState();
      expect(state.timeRemaining).toBe(1500000); // DEFAULT_DURATION in milliseconds
    });

    it('should initialize without segments parameter', () => {
      const defaultTimer = new Timer();
      const state = defaultTimer.getState();
      expect(state.timeRemaining).toBe(1500000); // DEFAULT_DURATION in milliseconds
    });
  });

  describe('start()', () => {
    it('should start the timer from stopped state', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const result = timer.start();

      expect(result.isRunning).toBe(true);
      expect(result.isPaused).toBe(false);
      expect(result.startedAt).toBe(mockTime);
      expect(result.startedSegment).toBe(0);
      expect(result.timePaused).toBe(0);
      expect(result.pausedAt).toBe(0);
    });

    it('should resume from paused state', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      // Start timer
      timer.start();

      // Advance time and pause
      advanceTime(5000);
      timer.pause();

      // Advance time while paused
      advanceTime(3000);

      // Resume (start from paused state)
      const result = timer.start();

      expect(result.isRunning).toBe(true);
      expect(result.isPaused).toBe(false);
      expect(result.pausedAt).toBe(0);
      expect(result.timePaused).toBe(3000);
    });

    it('should not reset timing when already running', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      const firstStartedAt = timer.getState().startedAt;

      advanceTime(2000);
      const result = timer.start();

      expect(result.startedAt).toBe(firstStartedAt);
      expect(result.isRunning).toBe(true);
    });
  });

  describe('pause()', () => {
    it('should pause the timer', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(5000);

      const result = timer.pause();

      expect(result.isPaused).toBe(true);
      expect(result.pausedAt).toBe(mockTime + 5000);
      expect(result.isRunning).toBe(true); // Still running, just paused
    });

    it('should work when timer is not running', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const result = timer.pause();

      expect(result.isPaused).toBe(true);
      expect(result.pausedAt).toBe(mockTime);
    });
  });

  describe('stop()', () => {
    it('should stop and reset the timer', () => {
      timer.start();
      advanceTime(5000);
      timer.pause();

      const result = timer.stop();

      expect(result.isRunning).toBe(false);
      expect(result.isPaused).toBe(false);
      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(25000); // 25 seconds in milliseconds
      expect(result.startedAt).toBe(0);
      expect(result.startedSegment).toBe(0);
      expect(result.pausedAt).toBe(0);
      expect(result.timePaused).toBe(0);
    });

    it('should reset to first segment duration', () => {
      const customSegments = [{ name: 'Custom', duration: 30, alert: 'Default', customCSS: '' }];
      const customTimer = new Timer(customSegments);

      customTimer.start();
      const result = customTimer.stop();

      expect(result.timeRemaining).toBe(30000); // 30 seconds in milliseconds
    });

    it('should handle empty segments', () => {
      const emptyTimer = new Timer([]);
      emptyTimer.start();

      const result = emptyTimer.stop();

      expect(result.timeRemaining).toBe(1500000); // DEFAULT_DURATION in milliseconds
    });
  });

  describe('repeat()', () => {
    it('should toggle repeat mode when no parameter provided', () => {
      expect(timer.getState().repeat).toBe(false);

      let result = timer.repeat();
      expect(result.repeat).toBe(true);

      result = timer.repeat();
      expect(result.repeat).toBe(false);
    });

    it('should set repeat mode explicitly', () => {
      let result = timer.repeat(true);
      expect(result.repeat).toBe(true);

      result = timer.repeat(false);
      expect(result.repeat).toBe(false);

      result = timer.repeat(true);
      expect(result.repeat).toBe(true);
    });
  });

  describe('next()', () => {
    it('should move to next segment', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const result = timer.next();

      expect(result.currentSegment).toBe(1);
      expect(result.timeRemaining).toBe(5000); // Break segment duration in milliseconds
    });

    it('should wrap around to first segment at end', () => {
      timer.setState({ currentSegment: 2 }); // Last segment

      const result = timer.next();

      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(25000); // Work segment duration in milliseconds
    });

    it('should update timing when running', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(5000);

      const result = timer.next();

      expect(result.startedAt).toBe(mockTime + 5000);
      expect(result.startedSegment).toBe(1);
      expect(result.timePaused).toBe(0);
    });

    it('should handle timing when paused and running', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      timer.pause();
      advanceTime(3000);

      const result = timer.next();

      expect(result.startedAt).toBe(mockTime + 3000);
      expect(result.pausedAt).toBe(mockTime + 3000);
    });

    it('should not update timing when stopped', () => {
      const result = timer.next();

      expect(result.startedAt).toBe(0);
      expect(result.startedSegment).toBe(0);
    });

    it('should handle empty segments gracefully', () => {
      const emptyTimer = new Timer([]);

      const result = emptyTimer.next();

      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(1500000); // DEFAULT_DURATION in milliseconds
    });
  });

  describe('resume()', () => {
    it('should resume from paused state', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      timer.pause();
      advanceTime(3000);

      const result = timer.resume();

      expect(result.isPaused).toBe(false);
      expect(result.pausedAt).toBe(0);
      expect(result.timePaused).toBe(3000);
    });

    it('should do nothing when not paused', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      const stateBefore = timer.getState();

      const result = timer.resume();

      expect(result.isPaused).toBe(false);
      expect(result.pausedAt).toBe(0);
      expect(result.timePaused).toBe(stateBefore.timePaused);
    });
  });

  describe('sync()', () => {
    it('should return unchanged state when not running', () => {
      const originalState = timer.getState();

      const result = timer.sync();

      expect(result).toEqual(originalState);
    });

    it('should return unchanged state when startedAt is 0', () => {
      timer.setState({ isRunning: true, startedAt: 0 });
      const originalState = timer.getState();

      const result = timer.sync();

      expect(result).toEqual(originalState);
    });

    it('should return unchanged state with empty segments', () => {
      const emptyTimer = new Timer([]);
      emptyTimer.setState({ isRunning: true, startedAt: 1000000 });
      const originalState = emptyTimer.getState();

      const result = emptyTimer.sync();

      expect(result).toEqual(originalState);
    });

    it('should calculate time remaining in current segment', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(10000); // 10 seconds elapsed

      const result = timer.sync();

      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(15000); // 25000 - 10000 milliseconds
    });

    it('should advance to next segment when current is complete', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(25000); // Complete first segment (25 seconds)

      const result = timer.sync();

      expect(result.currentSegment).toBe(1);
      expect(result.timeRemaining).toBe(5000); // Break segment duration in milliseconds
    });

    it('should handle multiple segment transitions', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(32000); // 25s + 5s + 2s into third segment

      const result = timer.sync();

      expect(result.currentSegment).toBe(2);
      expect(result.timeRemaining).toBe(13000); // 15000 - 2000 milliseconds
    });

    it('should stop timer at end when repeat is false', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(45000); // Complete all segments (25 + 5 + 15)

      const result = timer.sync();

      expect(result.isRunning).toBe(false);
      expect(result.isPaused).toBe(false);
      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(25000); // 25 seconds in milliseconds
      expect(result.startedAt).toBe(0);
      expect(result.startedSegment).toBe(0);
      expect(result.pausedAt).toBe(0);
      expect(result.timePaused).toBe(0);
    });

    it('should wrap around when repeat is enabled', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.repeat(true);
      timer.start();
      advanceTime(47000); // Complete cycle + 2s into first segment

      const result = timer.sync();

      expect(result.isRunning).toBe(true);
      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(23000); // 25000 - 2000 milliseconds
    });

    it('should handle paused timer correctly', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(5000);
      timer.pause();
      advanceTime(3000); // Time while paused

      const result = timer.sync();

      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(20000); // 25000 - 5000 milliseconds (pause time excluded)
    });

    it('should handle accumulated pause time', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(3000);
      timer.pause();
      advanceTime(2000);
      timer.resume();
      advanceTime(4000);
      timer.pause();
      advanceTime(1000);

      const result = timer.sync();

      expect(result.currentSegment).toBe(0);
      expect(result.timeRemaining).toBe(18000); // 25000 - 7000 milliseconds (excluding pause times)
    });

    it('should handle timer started from middle segment', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.setState({ currentSegment: 1, startedSegment: 1 });
      timer.start();
      advanceTime(3000);

      const result = timer.sync();

      expect(result.currentSegment).toBe(1);
      expect(result.timeRemaining).toBe(2000); // 5000 - 3000 milliseconds
    });
  });

  describe('updateFromSource()', () => {
    it('should update timer state from client data', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const clientState = {
        repeat: true,
        currentSegment: 1,
        timeRemaining: 3000, // 3 seconds in milliseconds
        isRunning: true,
        isPaused: false,
      };

      const result = timer.updateFromSource(clientState);

      expect(result.repeat).toBe(true);
      expect(result.currentSegment).toBe(1);
      expect(result.timeRemaining).toBe(3000);
      expect(result.isRunning).toBe(true);
      expect(result.isPaused).toBe(false);
    });

    it('should calculate startedAt from elapsed time', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const clientState = {
        currentSegment: 0,
        timeRemaining: 20000, // 5 seconds elapsed from 25000ms
        isRunning: true,
        isPaused: false,
      };

      const result = timer.updateFromSource(clientState);

      expect(result.startedAt).toBe(mockTime - 5000); // Started 5 seconds ago
      expect(result.startedSegment).toBe(0);
      expect(result.timePaused).toBe(0);
    });

    it('should handle stopped timer', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const clientState = {
        currentSegment: 1,
        timeRemaining: 5000, // 5 seconds in milliseconds
        isRunning: false,
        isPaused: false,
      };

      const result = timer.updateFromSource(clientState);

      expect(result.startedAt).toBe(0);
      expect(result.isRunning).toBe(false);
    });

    it('should handle paused timer', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const clientState = {
        currentSegment: 0,
        timeRemaining: 22000, // 22 seconds in milliseconds
        isRunning: true,
        isPaused: true,
      };

      const result = timer.updateFromSource(clientState);

      expect(result.isPaused).toBe(true);
      expect(result.pausedAt).toBe(mockTime);
      expect(result.timePaused).toBe(0);
    });

    it('should sanitize invalid input values', () => {
      const clientState = {
        repeat: 'true', // String will be assigned as-is
        currentSegment: -1,
        timeRemaining: -5000, // -5 seconds in milliseconds
        isRunning: 'false', // String will be assigned as-is
        isPaused: null,
      };

      const result = timer.updateFromSource(clientState);

      expect(result.repeat).toBe('true'); // updateFromSource doesn't sanitize
      expect(result.currentSegment).toBe(-1); // updateFromSource doesn't sanitize
      expect(result.timeRemaining).toBe(-5000); // updateFromSource doesn't sanitize
      expect(result.isRunning).toBe('false'); // updateFromSource doesn't sanitize
      expect(result.isPaused).toBe(null); // updateFromSource doesn't sanitize
    });

    it('should handle missing segment duration', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      const timerWithoutSegments = new Timer([]);

      const clientState = {
        currentSegment: 0,
        timeRemaining: 1400000, // 100 seconds elapsed from 1500000ms
        isRunning: true,
      };

      const result = timerWithoutSegments.updateFromSource(clientState);

      expect(result.startedAt).toBe(mockTime - 100000); // 1500000 - 1400000 = 100000ms elapsed
    });
  });

  describe('updateSegments()', () => {
    it('should update segments and maintain timer state', () => {
      const newSegments = [
        { name: 'New Work', duration: 30, alert: 'Default', customCSS: '' },
        { name: 'New Break', duration: 10, alert: 'Default', customCSS: '' },
      ];

      timer.updateSegments(newSegments);

      expect(timer.segments).toEqual(newSegments);
      const state = timer.getState();
      expect(state.timeRemaining).toBe(30000); // Updated to new first segment duration in milliseconds
    });

    it('should reset to first segment when current segment is out of bounds', () => {
      timer.setState({ currentSegment: 5 }); // Out of bounds

      const newSegments = [{ name: 'Only Segment', duration: 20, alert: 'Default', customCSS: '' }];

      timer.updateSegments(newSegments);

      const state = timer.getState();
      expect(state.currentSegment).toBe(0);
      expect(state.timeRemaining).toBe(20000); // 20 seconds in milliseconds
    });

    it('should handle empty segments array', () => {
      timer.updateSegments([]);

      const state = timer.getState();
      expect(state.currentSegment).toBe(0);
      expect(state.timeRemaining).toBe(1500000); // DEFAULT_DURATION in milliseconds
    });

    it('should recalculate timing for running timer', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(5000);

      const newSegments = [{ name: 'Updated Work', duration: 30, alert: 'Default', customCSS: '' }];

      timer.updateSegments(newSegments);

      const state = timer.getState();
      expect(state.startedAt).toBe(mockTime);
      expect(state.startedSegment).toBe(0);
      expect(state.timePaused).toBe(0);
      expect(state.pausedAt).toBe(0);
    });

    it('should reset timing for paused timer', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      timer.pause();

      const newSegments = [{ name: 'Updated Work', duration: 35, alert: 'Default', customCSS: '' }];

      timer.updateSegments(newSegments);

      const state = timer.getState();
      expect(state.startedAt).toBe(1000000); // Timer preserves startedAt for paused timers
      expect(state.timePaused).toBe(0);
      expect(state.pausedAt).toBe(1000000); // Timer preserves pausedAt for paused timers
    });

    it('should cap timeRemaining to new segment duration', () => {
      timer.setState({ timeRemaining: 30000, currentSegment: 0 }); // 30 seconds in milliseconds

      const newSegments = [{ name: 'Short Work', duration: 20, alert: 'Default', customCSS: '' }];

      timer.updateSegments(newSegments);

      const state = timer.getState();
      expect(state.timeRemaining).toBe(20000); // 20 seconds in milliseconds
    });

    it('should not cap timeRemaining when new duration is larger', () => {
      timer.setState({ timeRemaining: 15000, currentSegment: 0 }); // 15 seconds in milliseconds

      const newSegments = [{ name: 'Long Work', duration: 40, alert: 'Default', customCSS: '' }];

      timer.updateSegments(newSegments);

      const state = timer.getState();
      expect(state.timeRemaining).toBe(40000); // For stopped timer, timeRemaining is set to new duration
    });
  });

  describe('getState()', () => {
    it('should return a copy of current state', () => {
      const state1 = timer.getState();
      const state2 = timer.getState();

      expect(state1).toEqual(state2);
      expect(state1).not.toBe(state2); // Different object references

      // Modifying returned state should not affect timer
      state1.isRunning = true;
      expect(timer.getState().isRunning).toBe(false);
    });
  });

  describe('setState()', () => {
    it('should merge provided state with current state', () => {
      const newState = {
        isRunning: true,
        currentSegment: 2,
        timeRemaining: 10000, // 10 seconds in milliseconds
      };

      timer.setState(newState);
      const result = timer.getState();

      expect(result.isRunning).toBe(true);
      expect(result.currentSegment).toBe(2);
      expect(result.timeRemaining).toBe(10000);
      expect(result.repeat).toBe(false); // Original value preserved
      expect(result.isPaused).toBe(false); // Original value preserved
    });

    it('should handle partial state updates', () => {
      timer.setState({ repeat: true });

      const result = timer.getState();
      expect(result.repeat).toBe(true);
      expect(result.currentSegment).toBe(0); // Other values unchanged
    });

    it('should handle empty state object', () => {
      const originalState = timer.getState();
      timer.setState({});

      const result = timer.getState();
      expect(result).toEqual(originalState);
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete timer workflow', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      // Start timer
      timer.start();
      expect(timer.getState().isRunning).toBe(true);

      // Run for 10 seconds
      advanceTime(10000);
      let syncResult = timer.sync();
      expect(syncResult.timeRemaining).toBe(15000);

      // Pause for 5 seconds
      timer.pause();
      advanceTime(5000);
      syncResult = timer.sync();
      expect(syncResult.timeRemaining).toBe(15000); // Time unchanged while paused

      // Resume and run to completion
      timer.resume();
      advanceTime(15000); // Complete current segment

      syncResult = timer.sync();
      expect(syncResult.currentSegment).toBe(1); // Moved to break
      expect(syncResult.timeRemaining).toBe(5000);

      // Complete entire cycle
      advanceTime(5000 + 15000); // Break + Long break
      syncResult = timer.sync();
      expect(syncResult.isRunning).toBe(false); // Timer stopped
    });

    it('should handle repeat mode cycle', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.repeat(true);
      timer.start();

      // Complete full cycle and start again
      advanceTime(45000 + 5000); // Full cycle + 5s into next cycle

      const syncResult = timer.sync();
      expect(syncResult.isRunning).toBe(true);
      expect(syncResult.currentSegment).toBe(0);
      expect(syncResult.timeRemaining).toBe(20000); // 25000 - 5000
    });

    it('should handle segment updates during timer operation', () => {
      const mockTime = 1000000;
      setMockTime(mockTime);

      timer.start();
      advanceTime(10000);

      // Update segments while running
      const newSegments = [{ name: 'Modified Work', duration: 40, alert: 'Default', customCSS: '' }];
      timer.updateSegments(newSegments);

      const syncResult = timer.sync();
      expect(syncResult.currentSegment).toBe(0);
      expect(syncResult.timeRemaining).toBe(30000); // 40000 - 10000
    });
  });
});
