/**
 * Express + WebSocket server for collaborative timer (TypeScript)
 */

import type { ErrorMessage, Session } from './formatters.d';
import type { ServerWebSocket, HealthStats } from './server.d';
import { formatSession, formatPongMsg, formatErrorMsg } from './formatters';
import SessionManager from './sessions';
import express, { Express, Request, Response, NextFunction } from 'express';
import http, { Server as HttpServer, IncomingMessage } from 'http';
import WebSocket, { WebSocketServer } from 'ws';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config({ path: path.join(__dirname, '.env') });

/**
 * Timer server class
 */
class TimerServer {
  app: Express;
  server: HttpServer;
  wss: WebSocketServer | null;
  sessionManager: SessionManager;
  port: number;
  wsPort: number | string;

  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.sessionManager = new SessionManager();
    this.port = Number(process.env.PORT) || 3000;
    this.wsPort = Number(process.env.WS_PORT) || this.port;

    this.setupExpress();
    this.setupWebSocket();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup Express middleware and static serving
   *
   * @returns {void}
   */
  setupExpress(): void {
    // Trust proxy for reverse proxy
    this.app.set('trust proxy', 1);

    // Security headers
    this.app.use((_req: Request, res: Response, next: NextFunction) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      next();
    });

    // CORS for local development
    if (process.env.NODE_ENV === 'development') {
      this.app.use((req: Request, res: Response, next: NextFunction) => {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
        next();
      });
    }

    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Serve static files
    this.app.use(
      express.static(path.join(__dirname, '../public'), {
        maxAge: '1h',
        etag: true,
      })
    );

    // Serve shared files for client access
    this.app.use(
      '/js/shared',
      express.static(path.join(__dirname, '../shared'), {
        maxAge: '1h',
        etag: true,
        setHeaders: (res, filePath) => {
          if (filePath.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
          }
        },
      })
    );

    // Log all requests in development mode
    if (process.env.NODE_ENV === 'development') {
      this.app.use((req: Request, _res: Response, next: NextFunction) => {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
        next();
      });
    }
  }

  /**
   * Setup WebSocket server
   *
   * @returns {void}
   */
  setupWebSocket(): void {
    this.wss = new WebSocketServer({
      server: this.server,
      clientTracking: true,
      perMessageDeflate: false,
    });

    this.wss.on('connection', (ws: ServerWebSocket, req: IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.wss.on('error', (error: Error) => {
      console.error('WebSocket server error:', error);
    });

    setInterval(() => {
      if (!this.wss) return;

      this.wss.clients.forEach((ws: ServerWebSocket) => {
        if (ws.isAlive === false) {
          console.log('Terminating dead connection');
          ws.terminate();
          return;
        }
        ws.isAlive = false;
        ws.ping();
      });
    }, 30000);
  }

  /**
   * Setup HTTP routes
   *
   * @returns {void}
   */
  setupRoutes(): void {
    this.app.get('/health', (req: Request, res: Response) => {
      const api = req.headers['x-key-health'];
      const expected = process.env.HEALTH_KEY;

      if (!expected || api !== expected) {
        return res.redirect('/');
      }

      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        connections: this.wss ? this.wss.clients.size : 0,
      } as HealthStats);
    });

    this.app.get('/api/session/:sessionId', (req: Request, res: Response, next: NextFunction) => {
      const { sessionId } = req.params;
      const session = this.sessionManager.getSession(sessionId);
      if (!session) return next();
      res.json(formatSession(session) as Session);
    });

    this.app.get('/:sessionId?', (_req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });
  }

  /**
   * Setup error handling
   *
   * @returns {void}
   */
  setupErrorHandling(): void {
    this.app.use((error: Error, _req: Request, res: Response, next: NextFunction) => {
      console.error('Express error:', error);

      if (res.headersSent) {
        return next(error);
      }

      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
      });
    });

    process.on('uncaughtException', (error: Error) => {
      console.error('Uncaught Exception:', error);
      this.stop('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.stop('UNHANDLED_REJECTION');
    });

    process.on('SIGTERM', () => {
      console.log('SIGTERM received');
      this.stop('SIGTERM');
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received');
      this.stop('SIGINT');
    });
  }

  /**
   * Handle new WebSocket connection
   *
   * @param {ServerWebSocket} ws - WebSocket connection
   * @param {IncomingMessage} req - HTTP request
   * @returns {void}
   */
  handleWebSocketConnection(ws: ServerWebSocket, req: IncomingMessage): void {
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    console.log(`WebSocket connection from ${clientIp}`);

    ws.isAlive = true;
    ws.sessionId = null;
    ws.clientId = null;

    ws.on('pong', () => {
      ws.isAlive = true;
    });

    ws.on('message', (message: WebSocket.RawData) => {
      this.handleWebSocketMessage(ws, message);
    });

    ws.on('close', (code: number, reason: Buffer) => {
      this.handleWebSocketClose(ws, code, reason.toString());
    });

    ws.on('error', (error: Error) => {
      console.error(`WebSocket error for ${clientIp}:`, error);
    });
  }

  /**
   * Handle WebSocket message
   *
   * @param {ServerWebSocket} ws - WebSocket connection
   * @param {WebSocket.RawData} message - Message buffer
   * @returns {void}
   */
  handleWebSocketMessage(ws: ServerWebSocket, message: WebSocket.RawData): void {
    try {
      const data = JSON.parse(message.toString());

      if (data.type === 'ping') {
        ws.send(JSON.stringify(formatPongMsg()));
        return;
      }

      if (!data.type) {
        console.warn('Message without type received');
        return;
      }

      this.sessionManager.handleMessage(ws, data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      this.sendError(ws, 'Invalid message format');
    }
  }

  /**
   * Handle WebSocket connection close
   *
   * @param {ServerWebSocket} ws - WebSocket connection
   * @param {number} code - Close code
   * @param {string} reason - Close reason
   * @returns {void}
   */
  handleWebSocketClose(ws: ServerWebSocket, code: number, reason: string): void {
    console.log(`WebSocket closed: ${code} ${reason}`);

    if (ws.sessionId && ws.clientId) {
      this.sessionManager.removeClient(ws.sessionId, ws.clientId);
    }
  }

  /**
   * Send error message to WebSocket client
   *
   * @param {ServerWebSocket} ws - WebSocket connection
   * @param {string} message - Error message
   * @returns {void}
   */
  sendError(ws: ServerWebSocket, message: string): void {
    if (ws.readyState === WebSocket.OPEN) {
      const error = { message } as ErrorMessage;
      ws.send(JSON.stringify(formatErrorMsg(error)));
    }
  }

  /**
   * Start the server
   *
   * @returns {void}
   */
  start(): void {
    this.server.listen(this.port, () => {
      console.log(`Timer server running on port ${this.port}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`WebSocket: ws://localhost:${this.port}`);
      console.log(`Web UI: http://localhost:${this.port}`);
    });

    this.server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${this.port} is already in use`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
      }
    });
  }

  /**
   * Graceful shutdown
   *
   * @param {string} signal - Shutdown signal
   * @returns {void}
   */
  stop(signal: string): void {
    console.log(`Graceful shutdown initiated by ${signal}`);

    if (this.wss) {
      console.log('Closing WebSocket server...');
      this.wss.close(() => {
        console.log('WebSocket server closed');
      });
    }

    this.server.close(() => {
      console.log('HTTP server closed');
      process.exit(0);
    });

    setTimeout(() => {
      console.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }
}

const server = new TimerServer();
server.start();

export default TimerServer;
