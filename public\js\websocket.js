/**
 * WebSocket client manager
 */
class WebSocketClient {
  constructor(app = null) {
    this.app = app;
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.messageQueue = [];
    this.messageHandlers = new Map();
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;

    this.initialize();
  }

  /**
   * Initialize WebSocket connection
   */
  initialize() {
    this.setupMessageHandlers();
    this.updateConnectionStatus('disconnected');
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (this.isConnected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.updateConnectionStatus('connecting');

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port = window.location.port || (protocol === 'wss:' ? 443 : 80);
    const wsUrl = `${protocol}//${host}:${port === '80' || port === '443' ? '' : port}`;

    try {
      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.messageQueue = [];
    this.updateConnectionStatus('disconnected');
  }

  /**
   * Setup default message handlers
   */
  setupMessageHandlers() {
    this.onMessage('session_created', this.handleSessionCreated.bind(this));
    this.onMessage('session_joined', this.handleSessionJoined.bind(this));
    this.onMessage('segments_updated', this.handleSegmentsUpdated.bind(this));
    this.onMessage('timer_updated', this.handleTimerUpdated.bind(this));
    this.onMessage('user_connected', this.handleUserConnected.bind(this));
    this.onMessage('user_disconnected', this.handleUserDisconnected.bind(this));
    this.onMessage('user_updated', this.handleUserUpdated.bind(this));
    this.onMessage('connected_users', this.handleConnectedUsers.bind(this));
  }

  /**
   * Setup WebSocket event handlers
   */
  setupWebSocketHandlers() {
    this.ws.onopen = () => {
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.updateConnectionStatus('connected');
      this.startHeartbeat();
      this.processMessageQueue();

      console.log('WebSocket connected');
      Utils.Events.dispatch(document, 'websocketConnected');
    };

    this.ws.onclose = (event) => {
      this.handleDisconnection(event);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.handleConnectionError();
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event);
    };
  }

  /**
   * Handle WebSocket disconnection
   * @param {CloseEvent} event - Close event
   */
  handleDisconnection(event) {
    this.isConnected = false;
    this.isConnecting = false;
    this.stopHeartbeat();

    console.log('WebSocket disconnected:', event.code, event.reason);
    Utils.Events.dispatch(document, 'websocketDisconnected');

    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    } else {
      this.updateConnectionStatus('disconnected');
    }
  }

  /**
   * Handle connection error
   */
  handleConnectionError() {
    this.isConnecting = false;
    this.updateConnectionStatus('error');

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    this.updateConnectionStatus('reconnecting');

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      if (!this.isConnected) {
        console.log(`Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connect();
      }
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({
          type: 'ping',
        });

        // Set timeout for pong response
        this.heartbeatTimeout = setTimeout(() => {
          console.warn('Heartbeat timeout, closing connection');
          this.ws.close();
        }, 5000);
      }
    }, 30000);
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  /**
   * Handle incoming WebSocket message
   * @param {MessageEvent} event - Message event
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);

      // Handle pong response
      if (message.type === 'pong') {
        if (this.heartbeatTimeout) {
          clearTimeout(this.heartbeatTimeout);
          this.heartbeatTimeout = null;
        }
        return;
      }

      // Call registered message handler
      const handler = this.messageHandlers.get(message.type);
      if (handler) {
        handler(message);
      } else {
        console.warn('No handler for message type:', message.type);
      }

      // Dispatch generic message event
      Utils.Events.dispatch(document, 'websocketMessage', { message });
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Send message to server
   * @param {Object} message - Message object
   */
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        this.messageQueue.push(message);
      }
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
    }
  }

  /**
   * Process queued messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Register message handler
   * @param {string} messageType - Message type
   * @param {Function} handler - Handler function
   */
  onMessage(messageType, handler) {
    this.messageHandlers.set(messageType, handler);
  }

  /**
   * Unregister message handler
   * @param {string} messageType - Message type
   */
  offMessage(messageType) {
    this.messageHandlers.delete(messageType);
  }

  /**
   * Update connection status UI
   * @param {string} status - Connection status
   */
  updateConnectionStatus(status) {
    // TODO: Move to user component
    const userAvatarLarge = Utils.DOM.getId('user-avatar-large');
    const statusElement = Utils.DOM.getId('user-profile-btn');
    const statusText = Utils.DOM.getId('connection-status');

    if (!statusElement || !statusText) return;

    const statusClasses = ['connected', 'connecting', 'reconnecting', 'error', 'disconnected'];

    // Remove status classes from all elements (check for null)
    userAvatarLarge.classList.remove(...statusClasses);
    statusElement.classList.remove(...statusClasses);
    statusText.classList.remove(...statusClasses);

    switch (status) {
      case 'connected':
        userAvatarLarge.classList.add('connected');
        statusElement.classList.add('connected');
        statusText.classList.add('connected');
        statusText.textContent = 'Connected';
        statusText.style.display = 'none';
        break;
      case 'connecting':
        userAvatarLarge.classList.add('connecting');
        statusElement.classList.add('connecting');
        statusText.classList.add('connecting');
        statusText.textContent = 'Connecting...';
        statusText.style.display = 'inline';
        break;
      case 'reconnecting':
        userAvatarLarge.classList.add('connecting');
        statusElement.classList.add('connecting');
        statusText.classList.add('connecting');
        statusText.textContent = `Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`;
        statusText.style.display = 'inline';
        break;
      case 'error':
        userAvatarLarge.classList.add('error');
        statusElement.classList.add('error');
        statusText.classList.add('error');
        statusText.textContent = 'Connection failed';
        statusText.style.display = 'inline';
        break;
      default:
        userAvatarLarge.classList.add('disconnected');
        statusElement.classList.add('disconnected');
        statusText.classList.add('disconnected');
        statusText.textContent = 'Disconnected';
        statusText.style.display = 'inline';
        break;
    }
  }

  /**
   * Join or create a session
   * @param {string} sessionId - Session ID
   * @param {Object} user - User data (optional)
   */
  joinSession(sessionId, user = null) {
    const session = Utils.Storage.getSession(sessionId);
    user = user || session?.user || this.app.user?.getCurrentUser();

    this.send({
      type: 'join_session',
      sessionId,
      user: {
        clientId: user?.clientId || '',
        name: user?.name,
        avatarUrl: user?.avatarUrl,
      },
    });
  }

  /**
   * Update segments data
   * @param {string} sessionId - Session ID
   */
  updateSegments(sessionId) {
    const session = Utils.Storage.getSession(sessionId);

    this.send({
      type: 'segments_update',
      session: {
        name: session?.name,
        description: session?.description,
        segments: session?.segments || {},
      },
    });
  }

  /**
   * Start timer
   */
  startTimer() {
    this.send({
      type: 'timer_start',
    });
  }

  /**
   * Pause timer
   */
  pauseTimer() {
    this.send({
      type: 'timer_pause',
    });
  }

  /**
   * Stop timer
   */
  stopTimer() {
    this.send({
      type: 'timer_stop',
    });
  }

  /**
   * Toggle repeat mode
   */
  toggleRepeat(sessionId) {
    const session = Utils.Storage.getSession(sessionId);

    this.send({
      type: 'timer_repeat',
      repeat: Boolean(session?.timer?.repeat ?? false),
    });
  }

  /**
   * Move to next segment
   */
  next() {
    this.send({
      type: 'timer_next_segment',
    });
  }

  /**
   * Update timer state
   */
  updateTimer(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    const timer = session?.timer;

    this.send({
      type: 'timer_update',
      timer: {
        repeat: timer?.repeat,
        currentSegment: timer?.currentSegment,
        timeRemaining: timer?.timeRemaining,
        isRunning: timer?.isRunning,
        isPaused: timer?.isPaused,
      },
    });
  }

  /**
   * Update user profile
   * @param {Object} user - User data
   */
  updateUser(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    const user = session?.user;

    this.send({
      type: 'user_update',
      user: {
        name: user?.name,
        avatarUrl: user?.avatarUrl,
      },
    });
  }

  /**
   * Request connected users list
   */
  getConnectedUsers() {
    this.send({
      type: 'get_connected_users',
    });
  }

  // Message handlers
  handleSessionCreated(message) {
    Utils.Events.dispatch(document, 'sessionCreated', message);
  }

  handleSessionJoined(message) {
    Utils.Events.dispatch(document, 'sessionJoined', message);
  }

  handleSegmentsUpdated(message) {
    Utils.Events.dispatch(document, 'segmentsUpdated', {
      ...message,
      source: 'server',
    });
  }

  handleTimerUpdated(message) {
    Utils.Events.dispatch(document, 'timerUpdated', message);
  }

  handleUserConnected(message) {
    Utils.Events.dispatch(document, 'userConnected', message);
  }

  handleUserDisconnected(message) {
    Utils.Events.dispatch(document, 'userDisconnected', message);
  }

  handleUserUpdated(message) {
    Utils.Events.dispatch(document, 'userUpdated', message);
  }

  handleConnectedUsers(message) {
    Utils.Events.dispatch(document, 'connectedUsers', message);
  }

  /**
   * Get connection state
   * @returns {boolean} True if connected
   */
  isSocketConnected() {
    return this.isConnected;
  }
}

const initializeWebSocketClient = () => {
  Utils.Events.dispatch(document, 'webSocketClientReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeWebSocketClient);
} else {
  initializeWebSocketClient();
}

window.WebSocketClient = WebSocketClient;
