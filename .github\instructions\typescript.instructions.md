---
applyTo: '*.ts'
---

# TypeScript Guidelines

TypeScript specific guidelines for coding agents to follow.

## Coding

- ALWAYS use EXPLICIT types
- ALWAYS use EXPLICIT types
- ALWAYS use EXPLICIT types
- ALWAYS use EXPLICIT types
- DEFINE EXPLICIT types if absolutely necessary
- DEFINE EXPLICIT types if absolutely necessary
- <PERSON>FI<PERSON> EXPLICIT types if absolutely necessary
- DEFINE EXPLICIT types if absolutely necessary
- DO NOT use ANY, UNKNOWN or OBJECT, UNDEFINED types
- DO NOT use ANY, UNKNOWN or OBJECT, UNDEFINED types
- DO NOT use ANY, UNKNOWN or OBJECT, UNDEFINED types
- DO NOT use ANY, UNKNOWN or OBJECT, UNDEFINED types
- All functions and classes must have TypeDoc documentation with explicit parameter and return types for non-void functions.

# Linting

- DO NOT add eslint-disable-next-line comments UNLESS EXPLICTLY ASKED TO DO SO.
- DO NOT add comments to disable lint errors UNLESS EXPLICTLY ASKED TO DO SO.
- DO NOT add comments to ignore lint errors UNLESS EXPLICTLY ASKED TO DO SO.

## Migration

When migrating JavaScript code to TypeScript, the following guidelines MUST be followed:

- DO NOT omit code from the original file when converting to TypeScript.
- ENSURE ALL CODE IS MIGRATED FROM THE ORIGINAL FILE!
- ENSURE EVERY FUNCTION AND METHOD HAS BEEN MIGRATED!
- DO NOT OMIT ANY CODE FOR BREVITY!
- DO NOT OMIT ANY CODE AT ALL!
- Ensure exports are converted to ES module syntax.
- Ensure imports are converted to ES module syntax.
- Ensure all imports are correct and up to date.
- Ensure all exports are correct and up to date.
- When importing existing CommonJS modules in ES modules, use the following syntax:

```typescript
const formatters = require('./formatters');
const { formatErrorMsg, formatPongMsg, formatSession } = formatters;
```

## Examples

TypeDoc examples. These ONLY apply to TYPESCRIPT FILES. JavaScript files use JSDoc, see the JavaScript instructions for more details.

### Function Documentation

#### Returns a value

Note the blank line between the description and the `@param` tags.

```typescript
/**
 * Calculates the square root of a number.
 *
 * @param x the number to calculate the root of.
 * @returns the square root if `x` is non-negative or `NaN` if `x` is negative.
 */
export function sqrt(x: number): number {
  return Math.sqrt(x);
}
```

#### Void Functions

Note the lack of return type parameter in the documentation

```typescript
/**
 * Prints a string to the console.
 *
 * @param x the string to print.
 */
export function print(x: string): void {
  console.log(x);
}
```

Note there is no blank line or return type after the description.

```typescript
/**
 * Prints 'Hello World!' to the console.
 */
export function hello(): void {
  console.log('Hello World!');
}
```
