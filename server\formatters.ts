/**
 * Handle formatting and data sanitization
 */

import crypto from 'crypto';
import type {
  ConnectedUsersMessage,
  ErrorMessage,
  GetConnectedUsersMessage,
  IncomingMessage,
  JoinSessionMessage,
  PingMessage,
  PongMessage,
  Segment,
  Segments,
  SegmentsUpdatedMessage,
  SegmentsUpdateMessage,
  Session,
  SessionCreated,
  SessionCreatedMessage,
  SessionInternal,
  SessionJoined,
  SessionJoinedMessage,
  SessionNew,
  TimerNextSegmentMessage,
  TimerPauseMessage,
  TimerRepeatMessage,
  TimerStartMessage,
  TimerState,
  TimerStateInternal,
  TimerStopMessage,
  TimerUpdatedMessage,
  TimerUpdateMessage,
  UnknownIncomingMessage,
  User,
  UserConnectedMessage,
  UserDisconnectedMessage,
  UserInternal,
  UserUpdated,
  UserUpdatedMessage,
  UserUpdateMessage,
} from './formatters.d';
import {
  SESSION_ID_REGEX,
  CLIENT_ID_REGEX,
  MAX_STRING_LENGTH,
  MAX_NAME_LENGTH,
  MAX_URL_LENGTH,
  MAX_DURATION,
  MIN_DURATION,
  DEFAULT_DURATION,
} from '../shared/constants';
import TimerCore from '../shared/timer-core';

/**
 * Generate UUID v4
 *
 * @returns UUID string
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Hash string using SHA256
 *
 * @param input the string to hash
 * @returns the hashed string
 */
export function hashString(input: string): string {
  return crypto.createHash('sha256').update(input).digest('hex');
}

// =============================================================================
// BASE FORMATTERS
// =============================================================================

/**
 * Format session ID
 *
 * @param input the session ID string to format
 * @returns formatted session ID
 */
export function formatSessionId(input: string): string {
  const sessionId = input.trim();
  return SESSION_ID_REGEX.test(sessionId) ? sessionId : ''; // TODO: Generate a new ID if invalid?
}

/**
 * Format client ID
 *
 * @param input the client ID string to format
 * @returns formatted client ID
 */
export function formatClientId(input: string): string {
  const clientId = input.toLowerCase().trim();
  return CLIENT_ID_REGEX.test(clientId) ? clientId : generateUUID();
}

/**
 * Format user object
 *
 * @param user the user object to format
 * @returns formatted user object
 */
export function formatUser(user: User | UserInternal): User {
  return {
    clientId: user.clientId as string,
    name: user.name.trim().substring(0, MAX_NAME_LENGTH),
    avatarUrl: user.avatarUrl.trim().substring(0, MAX_URL_LENGTH),
    isOnline: 'offlineAt' in user ? !user.offlineAt : true, // offlineAt is only on UserInternal
  };
}

/**
 * Format segment object
 *
 * @param segment the segment object to format
 * @returns formatted segment object
 */
export function formatSegment(segment: Segment): Segment {
  return {
    name: segment.name.trim().substring(0, MAX_NAME_LENGTH),
    duration: Math.max(MIN_DURATION, Math.min(MAX_DURATION, segment.duration) || DEFAULT_DURATION),
    alert: segment.alert.trim().substring(0, MAX_NAME_LENGTH) || 'Default', // TODO: Should be limited to predefined values
    customCSS: segment.customCSS.trim(), // TODO: Sanitize CSS
  };
}

/**
 * Format segments collection
 *
 * @param segments the segments object to format
 * @returns formatted segments object
 */
export function formatSegments(segments: Segments): Segments {
  return {
    lastUpdated: segments.lastUpdated || Date.now(),
    items: Array.isArray(segments.items) ? segments.items.map(formatSegment) : [],
  };
}

/**
 * Format timer state object
 *
 * @param timer the timer state object to format
 * @returns formatted timer state object
 */
export function formatTimer(timer: TimerState | TimerStateInternal): TimerState {
  return { ...timer };
}

/**
 * Format session object
 *
 * @param session the session object to format
 * @returns formatted session object
 */
export function formatSession(session: Session | SessionInternal): Session {
  return {
    sessionId: formatSessionId(session.sessionId),
    name: session.name.trim().substring(0, MAX_STRING_LENGTH),
    description: session.description.trim().substring(0, MAX_STRING_LENGTH),
    segments: formatSegments(session.segments),
    timer: formatTimer(session.timer),
    users: new Map(
      Array.from(session?.users instanceof Map ? session.users : new Map()).map(([key, user]) => [
        key,
        formatUser(user),
      ])
    ),
  };
}

// =============================================================================
// INCOMING MESSAGE FORMATTERS
// =============================================================================

/**
 * Format join session message
 *
 * @param message the join session message to format
 * @returns formatted join message
 */
export function formatJoinMsg(message: JoinSessionMessage): JoinSessionMessage {
  return {
    type: 'join_session',
    sessionId: formatSessionId(message.sessionId),
    user: formatUser(message.user),
  };
}

/**
 * Format segments update message
 *
 * @param message the segments update message to format
 * @returns formatted segments update message
 */
export function formatSegUpdateMsg(message: SegmentsUpdateMessage): SegmentsUpdateMessage {
  const session = message.session;
  return {
    type: 'segments_update',
    session: {
      name: session.name.trim().substring(0, MAX_STRING_LENGTH),
      description: session.description.trim().substring(0, MAX_STRING_LENGTH),
      segments: formatSegments(session.segments),
    },
  };
}

/**
 * Format timer start message
 *
 * @returns formatted timer start message
 */
export function formatStartMsg(): TimerStartMessage {
  return { type: 'timer_start' };
}

/**
 * Format timer pause message
 *
 * @returns formatted timer pause message
 */
export function formatPauseMsg(): TimerPauseMessage {
  return { type: 'timer_pause' };
}

/**
 * Format timer stop message
 *
 * @returns formatted timer stop message
 */
export function formatStopMsg(): TimerStopMessage {
  return { type: 'timer_stop' };
}

/**
 * Format timer repeat message
 *
 * @param message the timer repeat message to format
 * @returns formatted timer repeat message
 */
export function formatRepeatMsg(message: TimerRepeatMessage): TimerRepeatMessage {
  return {
    type: 'timer_repeat',
    repeat: message.repeat,
  };
}

/**
 * Format timer next segment message
 *
 * @returns formatted timer next message
 */
export function formatNextMsg(): TimerNextSegmentMessage {
  return { type: 'timer_next_segment' };
}

/**
 * Format timer update message
 *
 * @param message the timer update message to format
 * @returns formatted timer update message
 */
export function formatUpdateMsg(message: TimerUpdateMessage): TimerUpdateMessage {
  return {
    type: 'timer_update',
    timer: formatTimer(message.timer),
  };
}

/**
 * Format user update message
 *
 * @param message the user update message to format
 * @returns formatted user update message
 */
export function formatUserUpdateMsg(message: UserUpdateMessage): UserUpdateMessage {
  return {
    type: 'user_update',
    user: formatUser(message.user),
  };
}

/**
 * Format get users message
 *
 * @returns formatted get users message
 */
export function formatGetUsersMsg(): GetConnectedUsersMessage {
  return { type: 'get_connected_users' };
}

/**
 * Format ping message
 *
 * @returns formatted ping message
 */
export function formatPingMsg(): PingMessage {
  return { type: 'ping' };
}

// =============================================================================
// OUTGOING MESSAGE FORMATTERS
// =============================================================================

/**
 * Format session created message
 *
 * @param message the session created object to format
 * @returns formatted session created message
 */
export function formatSessionCreatedMsg(message: SessionCreated): SessionCreatedMessage {
  return {
    type: 'session_created',
    sessionId: formatSessionId(message.sessionId),
    clientId: formatClientId(message.clientId),
  };
}

/**
 * Format session joined message
 *
 * @param message the session joined object to format
 * @returns formatted session joined message
 */
export function formatSessionJoinedMsg(message: SessionJoined): SessionJoinedMessage {
  return {
    type: 'session_joined',
    sessionId: formatSessionId(message.sessionId),
    clientId: formatClientId(message.clientId),
    session: formatSession(message.session),
  };
}

/**
 * Format user connected message
 *
 * @param message the user updated object to format
 * @returns formatted user connected message
 */
export function formatUserConnectedMsg(message: UserUpdated): UserConnectedMessage {
  return {
    type: 'user_connected',
    sessionId: formatSessionId(message.sessionId),
    user: formatUser(message.user),
  };
}

/**
 * Format user disconnected message
 *
 * @param message the user updated object to format
 * @returns formatted user disconnected message
 */
export function formatUserDisconnectedMsg(message: UserUpdated): UserDisconnectedMessage {
  return {
    type: 'user_disconnected',
    sessionId: formatSessionId(message.sessionId),
    user: formatUser(message.user),
  };
}

/**
 * Format user updated message
 *
 * @param message the user updated object to format
 * @returns formatted user updated message
 */
export function formatUserUpdatedMsg(message: UserUpdated): UserUpdatedMessage {
  return {
    type: 'user_updated',
    sessionId: formatSessionId(message.sessionId),
    user: formatUser(message.user),
  };
}

/**
 * Format connected users message
 *
 * @param session the session object to format
 * @returns formatted connected users message
 */
export function formatConnectedUsersMsg(session: SessionInternal): ConnectedUsersMessage {
  return {
    type: 'connected_users',
    sessionId: formatSessionId(session.sessionId),
    users: new Map(
      Array.from(session.users instanceof Map ? session.users : new Map()).map(([key, user]) => [key, formatUser(user)])
    ),
  };
}

/**
 * Format segments updated message
 *
 * @param session the session object to format
 * @returns formatted segments updated message
 */
export function formatSegmentsUpdatedMsg(session: SessionInternal): SegmentsUpdatedMessage {
  return {
    type: 'segments_updated',
    sessionId: formatSessionId(session.sessionId),
    session: {
      name: session.name.trim(),
      description: session.description.trim(),
      segments: formatSegments(session.segments),
    },
  };
}

/**
 * Format timer updated message
 *
 * @param session the session object to format
 * @returns formatted timer updated message
 */
export function formatTimerUpdatedMsg(session: SessionInternal): TimerUpdatedMessage {
  return {
    type: 'timer_updated',
    sessionId: formatSessionId(session.sessionId),
    timer: formatTimer(session.timer),
  };
}

/**
 * Format pong message
 *
 * @returns formatted pong message
 */
export function formatPongMsg(): PongMessage {
  return { type: 'pong' };
}

/**
 * Format error message
 *
 * @param error the error message object to format
 * @returns formatted error message
 */
export function formatErrorMsg(error: ErrorMessage): ErrorMessage {
  return {
    type: 'error',
    message: (typeof error.message === 'string' ? error.message.trim() : error.message) || 'Unknown error',
  };
}

// =============================================================================
// INTERNAL STRUCTURE FORMATTERS
// =============================================================================

/**
 * Format internal user object
 *
 * @param input the user object to format as internal
 * @returns formatted internal user object
 */
export function formatInternalUser(input: User | UserInternal): UserInternal {
  const user = input as UserInternal;
  return {
    ...formatUser(user),

    // Internal connection state, these properties do not exist in User type
    lastPing: user?.lastPing || Date.now(),
    offlineAt: user?.offlineAt || 0,
    ws: user?.ws || null,
  };
}

/**
 * Format timer state object (internal)
 *
 * @param input the timer state object to format as internal
 * @returns formatted timer state object (internal)
 */
export function formatInternalTimer(input: TimerState | TimerStateInternal): TimerStateInternal {
  const timer = input as TimerStateInternal;
  return {
    ...formatTimer(timer),

    // Internal timing state, these properties do not exist in TimerState type
    startedAt: timer?.startedAt || 0,
    startedSegment: timer?.startedSegment || 0,
    pausedAt: timer?.pausedAt || 0,
    timePaused: timer?.timePaused || 0,
  };
}

/**
 * Format session object (internal)
 *
 * @param input the session object to format as internal
 * @returns formatted session object (internal)
 */
export function formatInternalSession(input: Session | SessionInternal | SessionNew): SessionInternal {
  const session = input as SessionInternal;
  return {
    sessionId: formatSessionId(session.sessionId),
    name: (session?.name || '').trim().substring(0, MAX_STRING_LENGTH),
    description: (session?.description || '').trim().substring(0, MAX_STRING_LENGTH),
    segments: formatSegments(session?.segments || {}),
    timer: formatInternalTimer(session?.timer || {}),
    timerInstance: session?.timerInstance || new TimerCore(session?.segments?.items || []),
    users: new Map(
      Array.from(session?.users instanceof Map ? session.users : new Map()).map(([key, user]) => [
        key,
        formatInternalUser(user),
      ])
    ),
    createdAt: session?.createdAt || Date.now(),
    lastActivity: session?.lastActivity || Date.now(),
    emptyAt: session?.emptyAt || 0,
  };
}

// =============================================================================
// MAIN FORMATTER DISPATCHER
// =============================================================================

/**
 * Format incoming message with strict explicit typing.
 *
 * @param data the incoming message object
 * @returns the formatted IncomingMessage
 * @throws Error if message type is unknown
 */
export function formatIncoming(
  data:
    | JoinSessionMessage
    | SegmentsUpdateMessage
    | TimerStartMessage
    | TimerPauseMessage
    | TimerStopMessage
    | TimerNextSegmentMessage
    | TimerRepeatMessage
    | TimerUpdateMessage
    | UserUpdateMessage
    | GetConnectedUsersMessage
    | PingMessage
    | UnknownIncomingMessage
): IncomingMessage {
  switch (data.type) {
    case 'join_session':
      return formatJoinMsg(data);
    case 'segments_update':
      return formatSegUpdateMsg(data);
    case 'timer_start':
      return formatStartMsg();
    case 'timer_pause':
      return formatPauseMsg();
    case 'timer_stop':
      return formatStopMsg();
    case 'timer_repeat':
      return formatRepeatMsg(data);
    case 'timer_next_segment':
      return formatNextMsg();
    case 'timer_update':
      return formatUpdateMsg(data);
    case 'user_update':
      return formatUserUpdateMsg(data);
    case 'get_connected_users':
      return formatGetUsersMsg();
    case 'ping':
      return formatPingMsg();
    default:
      throw new Error('Invalid message type');
  }
}
