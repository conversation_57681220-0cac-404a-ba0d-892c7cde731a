/**
 * Utility functions for the timer application
 */

/**
 * Generate a unique client ID
 * @returns {string} UUID v4 string
 */
function generateClientId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Generate a unique session ID
 * @returns {string} Session ID string
 */
function generateSessionId() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate session ID format
 * @param {string} sessionId - Session ID to validate
 * @returns {boolean} True if valid
 */
function isValidSessionId(sessionId) {
  return /^[a-z0-9-]{3,64}$/.test(sessionId);
}

/**
 * Validate client ID format
 * @param {string} clientId - Client ID to validate
 * @returns {boolean} True if valid
 */
function isValidClientId(clientId) {
  return /^[a-f0-9-]{36}$/.test(clientId);
}

/**
 * Format time in seconds to MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
function formatTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(1, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Parse time string to seconds
 * @param {string} timeString - Time in MM:SS format
 * @returns {number} Time in seconds
 */
function parseTime(timeString) {
  const [minutes, seconds] = timeString.split(':').map(Number);
  return minutes * 60 + seconds;
}

/**
 * Get current timestamp
 * @returns {number} Current timestamp in milliseconds
 */
function getCurrentTimestamp() {
  return Date.now();
}

/**
 * Create default session structure
 * @param {string} sessionId - Session ID
 * @returns {Object} Default session object
 */
function createDefaultSession(sessionId = null) {
  return {
    id: sessionId,
    sessionId,
    name: 'Focus and Break Session',
    description: 'Focus and break timer',
    segments: {
      lastUpdated: getCurrentTimestamp(),
      items: [
        {
          name: 'Focus',
          duration: 1500, // 25 minutes
          alert: 'Gentle',
          customCSS: `/* Body Background */
body {
  background: linear-gradient(45deg, #ffeaa7, #fab1a0);
}

/* Timer Display Styling */
.timer-display {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
  border: none;
}

/* Timer Indicators */
.timer-btn {
  color: #c14242;
}

.timer-btn:hover {
  color: #971e1e;
}`,
        },
        {
          name: 'Break',
          duration: 300, // 5 minutes
          alert: 'Default',
          customCSS: `/* Body Background */
body {
  background: linear-gradient(45deg, #a8edea, #fed6e3);
}

/* Timer Display Styling */
.timer-display {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  box-shadow: 0 10px 30px rgba(78, 205, 196, 0.3);
  border: none;
  color: white;
}

/* Timer Indicators */
.timer-btn {
  color: #3c9382;
}

.timer-btn:hover {
  color: #236b6f;
}`,
        },
      ],
    },
    timer: {
      repeat: false,
      currentSegment: 0,
      timeRemaining: 1500000, // 25 minutes in milliseconds
      isRunning: false,
      isPaused: false,
      startedAt: 0,
      startedSegment: 0,
      pausedAt: 0,
      timePaused: 0,
    },
    users: [],
    user: {
      clientId: '',
      name: '',
      email: '',
      avatarUrl: '',
    },
  };
}

/**
 * Validate session object structure
 * @param {Object} session - Session object to validate
 * @returns {boolean} True if valid
 */
function validateSession(session) {
  if (!session || typeof session !== 'object') return false;

  // Check required properties
  const requiredProps = ['name', 'description', 'segments', 'timer', 'user'];
  if (!requiredProps.every((prop) => prop in session)) return false;

  // Check segments structure
  if (!session.segments.items || !Array.isArray(session.segments.items)) return false;
  if (session.segments.items.length === 0) return false;

  // Check each segment
  for (const segment of session.segments.items) {
    if (!segment.name || typeof segment.duration !== 'number' || segment.duration <= 0) {
      return false;
    }
  }

  return true;
}

/**
 * Get session ID from URL path
 * @returns {string|null} Session ID or null
 */
function getSessionIdFromUrl() {
  const path = window.location.pathname;
  const match = path.match(/^\/([a-z0-9-]{3,64})$/);
  return match ? match[1] : null;
}

/**
 * Set session ID in URL
 * @param {string} sessionId - Session ID
 */
function setSessionIdInUrl(sessionId) {
  const newUrl = `/${sessionId}`;
  if (window.location.pathname !== newUrl) {
    window.history.pushState({ sessionId }, '', newUrl);
  }
}

/**
 * Debounce function execution
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Copy text to clipboard using modern API
 * @param {string} text - Text to copy
 * @returns {Promise} Copy promise
 */
async function copyToClipboard(text) {
  // Try modern clipboard API first
  if (navigator.clipboard && navigator.clipboard.writeText) {
    return navigator.clipboard.writeText(text);
  }

  // Fallback to legacy method
  return legacyCopyToClipboard(text);
}

/**
 * Legacy clipboard copy method
 * @param {string} text - Text to copy
 * @returns {Promise} Copy promise
 */
function legacyCopyToClipboard(text) {
  return new Promise((resolve, reject) => {
    // Create temporary textarea
    const textarea = DOM.create('textarea', {
      value: text,
      style: 'position: fixed; top: -9999px; left: -9999px; opacity: 0;',
    });

    document.body.appendChild(textarea);

    try {
      // Select and copy text
      textarea.select();
      textarea.setSelectionRange(0, 99999); // For mobile devices

      const successful = document.execCommand('copy');
      document.body.removeChild(textarea);

      if (successful) {
        resolve();
      } else {
        reject(new Error('Copy command failed'));
      }
    } catch (error) {
      document.body.removeChild(textarea);
      reject(error);
    }
  });
}

/**
 * Show notification message
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 * @param {string} id - Notification ID (default: 'utility-notification')
 */
function showNotification(message, type = 'info', id = 'utility-notification') {
  // Remove existing notification
  const existing = DOM.getId(id);
  if (existing) {
    existing.remove();
  }

  const notification = DOM.create(
    'div',
    {
      id,
      className: `notification ${type}`,
    },
    message
  );

  document.body.appendChild(notification);

  // Auto-remove notification after 3 seconds
  setTimeout(() => {
    notification.style.animation = 'slideUpToTop 0.3s ease';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

/**
 * Crypto utility functions with fallbacks for insecure contexts
 */
const Crypto = {
  /**
   * Check if Web Crypto API is available
   * @returns {boolean} True if crypto.subtle is available
   */
  isWebCryptoAvailable() {
    return typeof crypto !== 'undefined' && crypto.subtle && typeof crypto.subtle.digest === 'function';
  },

  /**
   * Hash string using Web Crypto API or fallback
   * @param {string} input - String to hash
   * @returns {Promise<string>} Hashed string
   */
  async hashString(input) {
    if (this.isWebCryptoAvailable()) {
      return this.hashStringWebCrypto(input);
    } else {
      return this.hashStringFallback(input);
    }
  },

  /**
   * Hash string using Web Crypto API
   * @param {string} input - String to hash
   * @returns {Promise<string>} Hashed string
   */
  async hashStringWebCrypto(input) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(input);
      const buffer = await crypto.subtle.digest('SHA-256', data);
      const array = Array.from(new Uint8Array(buffer));
      return array.map((b) => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.warn('Web Crypto API failed, falling back to simple hash:', error);
      return this.hashStringFallback(input);
    }
  },

  /**
   * Fallback SHA-256 implementation for insecure contexts
   * Based on Chris Veness' MIT licensed implementation
   * @param {string} input - String to hash
   * @returns {Promise<string>} SHA-256 hash string
   */
  async hashStringFallback(input) {
    return this.sha256(input);
  },

  /**
   * SHA-256 implementation (MIT License - Chris Veness)
   * @param {string} msg - String to hash
   * @returns {string} SHA-256 hash as hex string
   */
  sha256(msg) {
    // Constants [§4.2.2]
    const K = [
      0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98,
      0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786,
      0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8,
      0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
      0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819,
      0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a,
      0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7,
      0xc67178f2,
    ];

    // Initial hash value [§5.3.3]
    const H = [0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19];

    // Convert string to UTF-8
    msg = this.utf8Encode(msg);

    // Preprocessing [§6.2.1]
    msg += String.fromCharCode(0x80); // Add trailing '1' bit (+ 0's padding)

    // Convert string msg into 512-bit blocks
    const l = msg.length / 4 + 2;
    const N = Math.ceil(l / 16);
    const M = new Array(N);

    for (let i = 0; i < N; i++) {
      M[i] = new Array(16);
      for (let j = 0; j < 16; j++) {
        M[i][j] =
          (msg.charCodeAt(i * 64 + j * 4 + 0) << 24) |
          (msg.charCodeAt(i * 64 + j * 4 + 1) << 16) |
          (msg.charCodeAt(i * 64 + j * 4 + 2) << 8) |
          (msg.charCodeAt(i * 64 + j * 4 + 3) << 0);
      }
    }

    // Add length (in bits) into final pair of 32-bit integers
    const lenHi = ((msg.length - 1) * 8) / Math.pow(2, 32);
    const lenLo = ((msg.length - 1) * 8) >>> 0;
    M[N - 1][14] = Math.floor(lenHi);
    M[N - 1][15] = lenLo;

    // Hash computation [§6.2.2]
    for (let i = 0; i < N; i++) {
      const W = new Array(64);

      // Prepare message schedule 'W'
      for (let t = 0; t < 16; t++) W[t] = M[i][t];
      for (let t = 16; t < 64; t++) {
        W[t] = (this.σ1(W[t - 2]) + W[t - 7] + this.σ0(W[t - 15]) + W[t - 16]) >>> 0;
      }

      // Initialize working variables
      let a = H[0],
        b = H[1],
        c = H[2],
        d = H[3],
        e = H[4],
        f = H[5],
        g = H[6],
        h = H[7];

      // Main loop
      for (let t = 0; t < 64; t++) {
        const T1 = h + this.Σ1(e) + this.Ch(e, f, g) + K[t] + W[t];
        const T2 = this.Σ0(a) + this.Maj(a, b, c);
        h = g;
        g = f;
        f = e;
        e = (d + T1) >>> 0;
        d = c;
        c = b;
        b = a;
        a = (T1 + T2) >>> 0;
      }

      // Compute new intermediate hash value
      H[0] = (H[0] + a) >>> 0;
      H[1] = (H[1] + b) >>> 0;
      H[2] = (H[2] + c) >>> 0;
      H[3] = (H[3] + d) >>> 0;
      H[4] = (H[4] + e) >>> 0;
      H[5] = (H[5] + f) >>> 0;
      H[6] = (H[6] + g) >>> 0;
      H[7] = (H[7] + h) >>> 0;
    }

    // Convert to hex strings
    for (let h = 0; h < H.length; h++) {
      H[h] = ('00000000' + H[h].toString(16)).slice(-8);
    }

    return H.join('');
  },

  /**
   * UTF-8 encoding helper
   * @param {string} str - String to encode
   * @returns {string} UTF-8 encoded string
   */
  utf8Encode(str) {
    try {
      return new TextEncoder().encode(str, 'utf-8').reduce((prev, curr) => prev + String.fromCharCode(curr), '');
    } catch (e) {
      return unescape(encodeURIComponent(str));
    }
  },

  /**
   * Rotate right (circular right shift)
   * @param {number} n - Number of positions
   * @param {number} x - Value to rotate
   * @returns {number} Rotated value
   */
  ROTR(n, x) {
    return (x >>> n) | (x << (32 - n));
  },

  /**
   * Logical functions [§4.1.2]
   */
  Σ0(x) {
    return this.ROTR(2, x) ^ this.ROTR(13, x) ^ this.ROTR(22, x);
  },
  Σ1(x) {
    return this.ROTR(6, x) ^ this.ROTR(11, x) ^ this.ROTR(25, x);
  },
  σ0(x) {
    return this.ROTR(7, x) ^ this.ROTR(18, x) ^ (x >>> 3);
  },
  σ1(x) {
    return this.ROTR(17, x) ^ this.ROTR(19, x) ^ (x >>> 10);
  },
  Ch(x, y, z) {
    return (x & y) ^ (~x & z);
  },
  Maj(x, y, z) {
    return (x & y) ^ (x & z) ^ (y & z);
  },
};

/**
 * Storage utility for LocalStorage operations
 */
const Storage = {
  /**
   * Get client ID from current session
   * @returns {string} Client ID
   */
  getClientId() {
    const sessionId = this.app.sessions?.getCurrentSessionId();
    if (sessionId) {
      const session = this.getSession(sessionId);
      if (session && session.user && session.user.clientId) {
        return session.user.clientId;
      }
    }
    return generateClientId();
  },

  /**
   * Get session data from storage
   * @returns {Object} Session data object
   */
  getSessionData() {
    try {
      const data = localStorage.getItem('session_data');
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error parsing session data:', error);
      return {};
    }
  },

  /**
   * Save session data to storage
   * @param {Object} data - Session data to save
   */
  saveSessionData(data) {
    try {
      localStorage.setItem('session_data', JSON.stringify(data));
    } catch (error) {
      console.error('Error saving session data:', error);
    }
  },

  /**
   * Get specific session by ID
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session object or null
   */
  getSession(sessionId) {
    const sessions = this.getSessionData();
    const session = sessions[sessionId];
    if (!session) return null;

    // TODO: Validate stored session data structure
    Object.assign(session, {
      ...session,
      users: [], // Ensure users array exists
    });

    return session;
  },

  /**
   * Save specific session
   * @param {string} sessionId - Session ID
   * @param {Object} session - Session object
   */
  saveSession(sessionId, session) {
    const sessions = this.getSessionData();
    const existing = sessions[sessionId];

    sessions[sessionId] = {
      sessionId,
      name: session.name,
      description: session.description,
      segments: session.segments,
      timer: session.timer,
      user: session.user || (existing && existing.user),
    };

    this.saveSessionData(sessions);
  },

  /**
   * Delete specific session
   * @param {string} sessionId - Session ID
   */
  deleteSession(sessionId) {
    const sessions = this.getSessionData();
    delete sessions[sessionId];
    this.saveSessionData(sessions);
  },

  /**
   * Get all session IDs
   * @returns {string[]} Array of session IDs
   */
  getAllSessionIds() {
    const sessions = this.getSessionData();
    return Object.keys(sessions);
  },

  /**
   * Clear all sessions
   */
  clearAllSessions() {
    localStorage.removeItem('session_data');
  },

  /**
   * Get client configuration from storage
   * @returns {Object} Client configuration object
   */
  getClientConfig() {
    try {
      const data = localStorage.getItem('client_config');
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error parsing client config:', error);
      return {};
    }
  },

  /**
   * Save client configuration to storage
   * @param {Object} config - Client configuration to save
   */
  saveClientConfig(config) {
    try {
      localStorage.setItem('client_config', JSON.stringify(config));
    } catch (error) {
      console.error('Error saving client config:', error);
    }
  },

  /**
   * Get specific client setting
   * @param {string} key - Setting key
   * @param {*} defaultValue - Default value if key doesn't exist
   * @returns {*} Setting value
   */
  getClientSetting(key, defaultValue = null) {
    const config = this.getClientConfig();
    return config.hasOwnProperty(key) ? config[key] : defaultValue;
  },

  /**
   * Save specific client setting
   * @param {string} key - Setting key
   * @param {*} value - Setting value
   */
  saveClientSetting(key, value) {
    const config = this.getClientConfig();
    config[key] = value;
    this.saveClientConfig(config);
  },
};

/**
 * DOM utility functions
 */
const DOM = {
  /**
   * Get element by ID
   * @param {string} id - Element ID
   * @returns {HTMLElement|null} Element or null
   */
  getId(id) {
    return document.getElementById(id);
  },

  /**
   * Query selector
   * @param {string} selector - CSS selector
   * @returns {HTMLElement|null} Element or null
   */
  query(selector) {
    return document.querySelector(selector);
  },

  /**
   * Query all selectors
   * @param {string} selector - CSS selector
   * @returns {NodeList} NodeList of elements
   */
  queryAll(selector) {
    return document.querySelectorAll(selector);
  },

  /**
   * Create element with attributes
   * @param {string} tag - HTML tag name
   * @param {Object} attributes - Attributes object
   * @param {string} textContent - Text content
   * @returns {HTMLElement} Created element
   */
  create(tag, attributes = {}, textContent = '') {
    const element = document.createElement(tag);

    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key.startsWith('data-')) {
        element.setAttribute(key, value);
      } else {
        element[key] = value;
      }
    });

    if (textContent) {
      element.textContent = textContent;
    }

    return element;
  },

  /**
   * Show modal
   * @param {string} modalId - Modal element ID
   */
  showModal(modalId) {
    const modal = this.getId(modalId);
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  },

  /**
   * Hide modal
   * @param {string} modalId - Modal element ID
   */
  hideModal(modalId) {
    const modal = this.getId(modalId);
    if (modal) {
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
  },

  /**
   * Toggle class on element
   * @param {HTMLElement} element - Target element
   * @param {string} className - Class name to toggle
   * @param {boolean} force - Force add/remove
   */
  toggleClass(element, className, force) {
    if (element) {
      element.classList.toggle(className, force);
    }
  },

  /**
   * Show popup with standard pattern
   * @param {string} popupId - Popup element ID
   */
  showPopup(popupId) {
    const popup = this.getId(popupId);
    if (popup) {
      popup.classList.remove('hidden');
    }
  },

  /**
   * Hide popup with standard pattern
   * @param {string} popupId - Popup element ID
   */
  hidePopup(popupId) {
    const popup = this.getId(popupId);
    if (popup) {
      popup.classList.add('hidden');
    }
  },

  /**
   * Toggle popup visibility
   * @param {string} popupId - Popup element ID
   */
  togglePopup(popupId) {
    const popup = this.getId(popupId);
    if (popup) {
      if (popup.classList.contains('hidden')) {
        this.showPopup(popupId);
      } else {
        this.hidePopup(popupId);
      }
    }
  },

  /**
   * Hide all popups
   */
  hideAllPopups() {
    const popups = this.queryAll('.popup');
    popups.forEach((popup) => {
      popup.classList.add('hidden');
    });
  },
};

/**
 * Event utility functions
 */
const Events = {
  /**
   * Add event listener
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} event - Event name
   * @param {Function} handler - Event handler
   * @param {boolean|Object} options - Event options
   */
  on(target, event, handler, options = false) {
    if (target && target.addEventListener) {
      target.addEventListener(event, handler, options);
    }
  },

  /**
   * Remove event listener
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} event - Event name
   * @param {Function} handler - Event handler
   * @param {boolean|Object} options - Event options
   */
  off(target, event, handler, options = false) {
    if (target && target.removeEventListener) {
      target.removeEventListener(event, handler, options);
    }
  },

  /**
   * Dispatch custom event
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} eventName - Event name
   * @param {Object} detail - Event data
   */
  dispatch(target, eventName, detail = {}) {
    if (target && target.dispatchEvent) {
      const event = new CustomEvent(eventName, { detail });
      target.dispatchEvent(event);
    }
  },
};

/**
 * Confirm dialog utility class
 */
class ConfirmDialog {
  constructor() {
    this.isOpen = false;
    this.currentResolve = null;

    this.initialize();
  }

  initialize() {
    this.setupEventListeners();

    Events.dispatch(document, 'confirmDialogReady');
  }

  /**
   * Setup event listeners for the confirm dialog
   */
  setupEventListeners() {
    const modal = DOM.getId('confirm-modal');
    const okBtn = DOM.getId('confirm-ok-btn');
    const cancelBtn = DOM.getId('confirm-cancel-btn');

    // Button click handlers
    Events.on(okBtn, 'click', () => this.resolve(true));
    Events.on(cancelBtn, 'click', () => this.resolve(false));

    // Keyboard handlers
    Events.on(document, 'keydown', (e) => {
      if (!this.isOpen) return;

      switch (e.key) {
        case 'Enter':
          e.preventDefault();
          this.resolve(true);
          break;
        case 'Escape':
          e.preventDefault();
          this.resolve(false);
          break;
      }
    });

    // Click outside to cancel (optional)
    Events.on(modal, 'click', (e) => {
      if (e.target === modal) {
        this.resolve(false);
      }
    });
  }

  /**
   * Show confirm dialog with a message
   * @param {string} message - Message to display
   * @param {Object} options - Optional configuration
   * @param {string} options.okText - Text for OK button (default: 'OK')
   * @param {string} options.cancelText - Text for Cancel button (default: 'Cancel')
   * @returns {Promise<boolean>} Promise that resolves to true if confirmed, false if cancelled
   */
  show(message, options = {}) {
    return new Promise((resolve) => {
      if (this.isOpen) {
        // If dialog is already open, reject the previous promise and start new one
        if (this.currentResolve) {
          this.currentResolve(false);
        }
      }

      this.currentResolve = resolve;
      this.isOpen = true;

      // Set message
      const messageEl = DOM.getId('confirm-message');
      messageEl.textContent = message;

      // Set button text
      const okBtn = DOM.getId('confirm-ok-btn');
      const cancelBtn = DOM.getId('confirm-cancel-btn');
      okBtn.textContent = options.okText || 'OK';
      cancelBtn.textContent = options.cancelText || 'Cancel';

      // Show modal
      DOM.showModal('confirm-modal');

      // Focus the cancel button by default for safety
      setTimeout(() => {
        cancelBtn.focus();
      }, 100);
    });
  }

  /**
   * Resolve the current promise and hide the dialog
   * @param {boolean} result - Result to resolve with
   */
  resolve(result) {
    if (!this.isOpen || !this.currentResolve) return;

    this.isOpen = false;
    const resolve = this.currentResolve;
    this.currentResolve = null;

    // Hide modal
    DOM.hideModal('confirm-modal');

    // Resolve promise
    resolve(result);
  }

  /**
   * Check if dialog is currently open
   * @returns {boolean} True if dialog is open
   */
  isDialogOpen() {
    return this.isOpen;
  }
}

// Create global confirm dialog instance
const confirmDialog = new ConfirmDialog();

/**
 * Show confirm dialog - convenience function
 * @param {string} message - Message to display
 * @param {Object} options - Optional configuration
 * @returns {Promise<boolean>} Promise that resolves to true if confirmed, false if cancelled
 */
function showConfirm(message, options = {}) {
  return confirmDialog.show(message, options);
}

// Export utilities to global scope
window.Utils = {
  generateClientId,
  generateSessionId,
  isValidSessionId,
  isValidClientId,
  formatTime,
  parseTime,
  debounce,
  getCurrentTimestamp,
  copyToClipboard,
  legacyCopyToClipboard,
  showNotification,
  showConfirm,
  Crypto,
  Storage,
  createDefaultSession,
  validateSession,
  getSessionIdFromUrl,
  setSessionIdInUrl,
  DOM,
  Events,
};

Events.dispatch(document, 'utilsReady');
