# Timeshare - Collaborative Focus Timer

A real-time collaborative Pomodoro-like timer application that allows multiple users to sync their focus sessions across devices. Built with vanilla JavaScript and WebSockets for seamless real-time collaboration.

## Features

- **Real-time Collaboration**: Multiple users can join the same session and stay synchronized
- **Custom Segments**: Create unlimited work and break segments with custom durations
- **Session Management**: URL-based session sharing with persistent local storage
- **User Profiles**: Gravatar integration with online status indicators
- **Audio Alerts**: Customizable alert sounds for segment transitions
- **Custom Styling**: Apply custom CSS to segments for personalized experiences
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Offline Resilience**: Continues working offline with automatic reconnection

## Technology Stack

- **Frontend**: Vanilla JavaScript (ES6+), HTML5, CSS3
- **Backend**: Node.js, Express, WebSocket (ws)
- **Storage**: LocalStorage for client-side persistence
- **Real-time**: WebSocket for live synchronization
- **External APIs**: Gravatar for user avatars

## Quick Start

### Prerequisites

- Node.js 16.0.0 or higher
- npm or yarn package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd focustime
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Create environment configuration**

   ```bash
   cp server/.env.example server/.env
   ```

4. **Start the development server**

   ```bash
   npm start
   ```

5. **Open in browser**
   ```
   http://localhost:3000
   ```

## Usage

### Creating a Session

1. Open the application in your browser
2. A new session will be automatically created with a unique URL
3. Customize your timer segments in the Settings panel
4. Share the URL with others to collaborate

### Joining a Session

1. Click on a shared session URL
2. The app will automatically join the session
3. Your timer will sync with other participants
4. Set your profile name and email for identification

### Timer Controls

- **Spacebar**: Start/pause the timer
- **Settings Button**: Configure segments and session details
- **Share Button**: Copy session URL to clipboard
- **User Button**: Edit your profile information
- **Sessions Button**: View and manage all your sessions

### Keyboard Shortcuts

- `Space` - Start/pause timer
- `Esc` - Close modals
- `Ctrl/Cmd + S` - Open settings
- `Ctrl/Cmd + U` - Open user profile
- `Ctrl/Cmd + L` - Open sessions list

## Configuration

### Environment Variables

Create a `server/.env` file with the following options:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# WebSocket Configuration
WS_PORT=8080

# Session Configuration
SESSION_CLEANUP_INTERVAL=300000
MAX_SESSIONS=1000
MAX_USERS_PER_SESSION=50

# Security
ALLOWED_ORIGINS=http://localhost:3000
```

### Custom Segments

Each segment can be customized with:

- **Name**: Descriptive label for the segment
- **Duration**: Length in minutes (1-1440)
- **Alert Sound**: Audio notification when segment ends
- **Custom CSS**: Styling rules applied during the segment

### Alert Sounds

Available built-in alert sounds:

- Default Bell
- Gentle Chime
- Church Bell
- Notification
- Urgent Alert
- Peaceful Tone
- Digital Beep
- Classic Alarm

## API Endpoints

### Health Check

```
GET /health
```

Returns server status and statistics.

### Session Info

```
GET /api/session/:sessionId
```

Returns public information about a session.

### WebSocket Messages

The application uses WebSocket for real-time communication:

#### Client → Server

- `join_session` - Join or create a session
- `segments_update` - Update segment configurations
- `timer_start` - Start the timer
- `timer_pause` - Pause the timer
- `timer_stop` - Stop and reset the timer
- `timer_repeat` - Toggle repeat mode
- `user_update` - Update user profile

#### Server → Client

- `session_created` - New session created
- `session_joined` - Joined existing session
- `segments_updated` - Segment data updated
- `timer_updated` - Timer state updated
- `user_connected` - User joined session
- `user_disconnected` - User left session
- `user_updated` - User profile updated

## Development

### Project Structure

```
focustime/
├── docs/                   # Documentation and specifications
├── public/                 # Frontend static files
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript modules
│   └── index.html         # Main HTML file
├── server/                # Backend server code
│   ├── server.js          # Express + WebSocket server
│   └── sessions.js        # Session management
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with auto-reload
- `npm test` - Run tests (when implemented)
- `npm run build` - Build for production (no build needed for vanilla JS)

### Development Guidelines

- Use ES6+ JavaScript features
- Follow the existing code style (Prettier configured)
- Write JSDoc comments for functions
- Keep components modular and focused
- Test real-time features with multiple browser tabs

### Adding New Features

1. **Frontend Components**: Add new JavaScript modules in `public/js/`
2. **Backend Handlers**: Extend message handlers in `server/sessions.js`
3. **Styling**: Add CSS rules in `public/css/styles.css`
4. **Documentation**: Update this README and add JSDoc comments

## Browser Support

- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 11+, Edge 79+
- **WebSocket Support**: Required for real-time features
- **LocalStorage**: Required for session persistence
- **Web Audio API**: Required for alert sounds

## Security Considerations

- Input validation on both client and server
- WebSocket message schema validation
- XSS prevention in custom CSS
- Rate limiting for message handling
- Session cleanup for resource management

## Performance

- Efficient DOM updates with minimal reflows
- Debounced input handling for real-time updates
- WebSocket connection pooling
- Automatic cleanup of inactive sessions
- Memory management for long-running sessions

## Deployment

### Production Setup

1. Set `NODE_ENV=production` in environment
2. Configure reverse proxy (nginx recommended)
3. Set up SSL certificates for secure WebSocket
4. Configure process manager (PM2 recommended)
5. Set up monitoring and logging

### Docker Deployment

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the coding standards
4. Test with multiple users and browsers
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For issues, questions, or contributions:

1. Check the [Issues](../../issues) page
2. Review the [Documentation](docs/)
3. Create a new issue with detailed information

## Roadmap

- [ ] Persistent storage with database backend
- [ ] Mobile app versions (React Native)
- [ ] Team management and permissions
- [ ] Advanced analytics and reporting
- [ ] Integration with productivity tools
- [ ] Themes and customization options
- [ ] Offline mode improvements
- [ ] Push notifications for mobile
