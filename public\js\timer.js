/**
 * Timer manager class
 */
class Timer {
  constructor(app = null) {
    this.app = app;
    this.session = null;
    this.sessionId = null;
    this.intervalId = null;
    this.timerCore = null;

    this.initialize();
  }

  /**
   * Initialize timer manager
   */
  initialize() {
    this.setupEventListeners();
    this.renderHtml();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    const elements = getTimerElements();

    Utils.Events.on(elements.startBtn, 'click', this.start.bind(this));
    Utils.Events.on(elements.pauseBtn, 'click', this.pause.bind(this));
    Utils.Events.on(elements.stopBtn, 'click', this.stop.bind(this));
    Utils.Events.on(elements.nextBtn, 'click', this.next.bind(this));
    Utils.Events.on(elements.repeatBtn, 'click', this.repeat.bind(this));
  }

  /**
   * Update timer state based on event type
   * @param {string} type - WebSocket event type
   * @param {Object} response - Event response data
   */
  async updateData(type, response) {
    // Session data is already updated by Coordinator, read from localStorage
    const sessionId = response.sessionId || this.sessionId;
    if (!sessionId) return;

    const session = Utils.Storage.getSession(sessionId);
    if (!session) return;

    this.loadSession(sessionId);

    // Update internal state based on event type
    switch (type) {
      case 'sessionCreated':
        this.sessionId = sessionId;
        this.session = session;

        // Initialize TimerCore with session segments
        this.timerCore = new TimerCore(this.session.segments.items);

        // Initialize timer state from session
        if (this.session.timer) {
          this.timerCore.updateFromSource(this.session.timer);
        }
        break;

      case 'sessionJoined':
        this.sessionId = sessionId;
        this.session = session; // Read from localStorage (already updated by Coordinator)

        // Initialize TimerCore with session segments
        this.timerCore = new TimerCore(this.session.segments.items);

        // Update TimerCore with timer state
        if (this.session.timer) {
          this.timerCore.updateFromSource(this.session.timer);
        }

        // Start local timer if running
        if (this.session.timer.isRunning && !this.session.timer.isPaused) {
          this.startTimer();
        }
        break;

      case 'segmentsUpdated':
        // Update session reference
        this.session = session;

        // Update TimerCore with new segments
        if (this.timerCore) {
          this.timerCore.updateSegments(this.session.segments.items);
          this.session.timer = this.timerCore.getState();
          Utils.Storage.saveSession(sessionId, this.session);
        }
        break;

      case 'timerUpdated':
        // Update session reference
        this.session = session;

        // Update TimerCore with server timer state
        if (this.timerCore) {
          this.timerCore.updateFromSource(this.session.timer);
        }

        // Start or stop local timer based on state
        if (this.session.timer.isRunning && !this.session.timer.isPaused) {
          this.startTimer();
        } else {
          this.stopTimer();
        }
        break;
    }
  }

  /**
   * Start the timer
   */
  start() {
    if (!this.sessionId || !this.session || !this.timerCore) {
      console.warn('No active session to start timer');
      return;
    }

    // Update session state from TimerCore
    this.session.timer = this.timerCore.start();

    // Save to storage
    Utils.Storage.saveSession(this.sessionId, this.session);

    // Send to server
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.startTimer(this.sessionId);
    }

    this.startTimer();
    this.renderHtml();
  }

  /**
   * Pause the timer
   */
  pause() {
    if (!this.sessionId || !this.session || !this.timerCore) {
      console.warn('No active session to pause timer');
      return;
    }

    // Update session state from TimerCore
    this.session.timer = this.timerCore.pause();

    // Save to storage
    Utils.Storage.saveSession(this.sessionId, this.session);

    // Send to server
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.pauseTimer();
    }

    this.stopTimer();
    this.renderHtml();
  }

  /**
   * Stop the timer and reset
   */
  stop() {
    if (!this.sessionId || !this.session || !this.timerCore) {
      console.warn('No active session to stop timer');
      return;
    }

    // Update session state from TimerCore
    this.session.timer = this.timerCore.stop();

    // Save to storage
    Utils.Storage.saveSession(this.sessionId, this.session);

    // Send to server
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.stopTimer();
    }

    this.stopTimer();
    this.renderHtml();
  }

  /**
   * Move to next segment
   */
  next() {
    if (!this.sessionId || !this.session || !this.timerCore) {
      console.warn('No active session to move to next segment');
      return;
    }

    // Update session state from TimerCore
    this.session.timer = this.timerCore.next();

    // Save to storage
    Utils.Storage.saveSession(this.sessionId, this.session);

    // Send to server
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.next();
    }

    this.renderHtml();
  }

  /**
   * Toggle repeat mode
   */
  repeat() {
    if (!this.sessionId || !this.session || !this.timerCore) {
      console.warn('No active session to toggle repeat mode');
      return;
    }

    // Update session state from TimerCore
    this.session.timer = this.timerCore.repeat(!this.session.timer.repeat);

    // Save to localStorage
    Utils.Storage.saveSession(this.sessionId, this.session);

    // Sync with server
    if (this.app.socket.isSocketConnected()) {
      this.app.socket.toggleRepeat(this.sessionId);
    }

    // Update display
    this.renderHtml();
  }

  /**
   * Calculate timer state
   */
  sync() {
    if (!this.session || !this.timerCore) return;
    this.session.timer = this.timerCore.sync();
  }

  /**
   * Start local timer interval
   */
  startTimer() {
    this.stopTimer();
    this.intervalId = setInterval(() => {
      this.tickTimer();
    }, 200);
  }

  /**
   * Stop local timer interval
   */
  stopTimer() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Update timer tick and display
   */
  tickTimer() {
    if (!this.session || !this.timerCore) return;

    const oldSegment = this.session.timer.currentSegment;

    // Sync timer state
    this.sync();

    // Play alert if segment changed
    if (oldSegment !== this.session.timer.currentSegment) {
      const segment = this.session.segments.items[oldSegment];
      this.app.alerts.playAlert(segment.alert);

      this.session.timer = this.timerCore.getState();
      Utils.Storage.saveSession(this.sessionId, this.session);
    }

    // Update display
    this.renderHtml();
  }

  /**
   * Render timer HTML based on current data
   */
  async renderHtml() {
    const currentSegment = this.getCurrentSegment();
    if (currentSegment && currentSegment.customCSS) {
      this.applyCustomCSS(currentSegment.customCSS);
    }

    this.renderTimer();
    this.renderTimerState();
    this.renderRepeatButton();
  }

  /**
   * Update timer display
   */
  renderTimer() {
    const elements = getTimerElements();

    if (!this.session) {
      elements.timerDisplay.textContent = '25:00';
      elements.segmentName.textContent = 'Focus';
      elements.segmentStatus.textContent = '1/2';
      return;
    }

    // Update timer display
    elements.timerDisplay.textContent = Utils.formatTime(Math.ceil(this.session.timer.timeRemaining / 1000));

    // Update segment info
    const currentSegment = this.getCurrentSegment();
    if (currentSegment) elements.segmentName.textContent = currentSegment.name;

    const current = this.session.timer.currentSegment + 1;
    const total = this.session.segments.items.length;
    elements.segmentStatus.textContent = `${current}/${total}`;

    // Update button states
    const isRunning = this.session.timer.isRunning;
    const isPaused = this.session.timer.isPaused;

    elements.startBtn.style.display = !isRunning || isPaused ? 'flex' : 'none';
    elements.pauseBtn.style.display = isRunning && !isPaused ? 'flex' : 'none';
    elements.nextBtn.style.display = 'flex';
  }

  /**
   * Update timer state classes on body
   */
  renderTimerState() {
    const body = document.body;

    if (!this.session) {
      body.className = 'timer-stopped';
      return;
    }

    const isRunning = this.session.timer.isRunning;
    const isPaused = this.session.timer.isPaused;

    body.classList.remove('timer-running', 'timer-paused', 'timer-stopped', 'timer-repeat');

    if (isRunning && !isPaused) {
      body.classList.add('timer-running');
    } else if (isRunning && isPaused) {
      body.classList.add('timer-running', 'timer-paused');
    } else {
      body.classList.add('timer-stopped');
    }

    if (this.session.timer.repeat) {
      body.classList.add('timer-repeat');
    }
  }

  /**
   * Update repeat mode button display based on current state
   */
  renderRepeatButton() {
    const elements = getTimerElements();
    if (!this.session) return;
    const repeat = this.session.timer.repeat;
    elements.repeatBtn.title = repeat ? 'Click to disable repeat mode' : 'Click to enable repeat mode';
    elements.repeatLine.style.display = repeat ? 'none' : 'block';
  }

  /**
   * Load session data and initialize timer
   * TODO: Determine if this function is necessary.
   *       It is being called in session.js importSessionConfig
   * @param {string} sessionId - Session ID
   */
  loadSession(sessionId) {
    this.sessionId = sessionId;
    this.session = Utils.Storage.getSession(sessionId);

    // Initialize TimerCore with session segments
    this.timerCore = new TimerCore(this.session.segments.items);

    // Sync timer state from stored session
    if (this.session.timer) {
      this.timerCore.updateFromSource(this.session.timer);
    }

    this.renderHtml();
  }

  /**
   * Apply custom CSS from segment
   * @param {string} css - Custom CSS string
   */
  applyCustomCSS(css) {
    const existingStyle = Utils.DOM.getId('custom-segment-css');
    if (existingStyle) existingStyle.remove();

    if (css.trim()) {
      const style = Utils.DOM.create('style', {
        id: 'custom-segment-css',
        type: 'text/css',
      });
      style.textContent = css;
      document.head.appendChild(style);
    }
  }

  /**
   * Get current segment object
   * @returns {Object|null} Current segment or null
   */
  getCurrentSegment() {
    if (!this.session || !this.session?.segments?.items || !this.timerCore) {
      return null;
    }

    const state = this.timerCore.getState();
    const index = state.currentSegment;

    return this.session.segments.items[index] || null;
  }

  /**
   * Get current timer state
   * @returns {Object} Timer state
   */
  getTimerState() {
    if (!this.timerCore) {
      return {
        isRunning: false,
        isPaused: false,
        currentSegment: 0,
        timeRemaining: 1500000, // 25 minutes in milliseconds
        repeat: false,
        startedSegment: 0,
        startedAt: 0,
        pausedAt: 0,
        timePaused: 0,
      };
    }

    return this.timerCore.getState();
  }

  /**
   * Get current session (compatibility for settings.js)
   * TODO: Refactor to remove this method
   * @returns {Object|null} Current session
   */
  getCurrentSession() {
    return this.session;
  }

  /**
   * Update segment duration (for settings.js integration)
   * TODO: Confirm this methods works as expected
   * @param {number} segmentIndex - Index of segment to update
   * @param {number} newDuration - New duration in seconds
   */
  handleSegmentDuration(segmentIndex, newDuration) {
    if (!this.session || !this.timerCore) return;

    if (this.session.segments.items[segmentIndex]) {
      this.session.segments.items[segmentIndex].duration = newDuration;
    }

    this.timerCore.updateSegments(this.session.segments.items);
    this.session.timer = this.timerCore.getState();
  }

  /**
   * Handle segment deletion (for settings.js integration)
   * TODO: Confirm this methods works as expected
   */
  handleSegmentDeletion() {
    if (!this.session || !this.timerCore) return;
    this.timerCore.updateSegments(this.session.segments.items);
    this.session.timer = this.timerCore.getState();
  }

  /**
   * Dispose timer resources
   */
  dispose() {
    this.stopTimer();
    this.session = null;
    this.sessionId = null;
    this.timerCore = null;
  }
}

/**
 * Retrieve all timer-related DOM elements
 * @returns {Object} Timer DOM elements
 */
const getTimerElements = () => ({
  startBtn: Utils.DOM.getId('start-btn'),
  pauseBtn: Utils.DOM.getId('pause-btn'),
  stopBtn: Utils.DOM.getId('stop-btn'),
  nextBtn: Utils.DOM.getId('next-btn'),
  repeatBtn: Utils.DOM.getId('repeat-btn'),
  timerDisplay: Utils.DOM.getId('timer-text'),
  segmentName: Utils.DOM.getId('segment-name'),
  segmentStatus: Utils.DOM.getId('segment-status'),
  repeatLine: Utils.DOM.getId('repeat-disabled-line'),
});

const initializeTimer = () => {
  Utils.Events.dispatch(document, 'timerReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeTimer);
} else {
  initializeTimer();
}

window.Timer = Timer;
