services:
  service:
    image: focustime_dev
    container_name: focustime_dev
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    environment:
      - NODE_ENV=development
      - PORT=3000
    volumes:
      # Mount source code for hot reload in development
      - ./server:/usr/src/app/server:ro
      - ./public:/usr/src/app/public:ro
      - ./shared:/usr/src/app/shared:ro
    command: ['npm', 'run', 'dev']
    ports:
      - 3000:3000
      - 9229:9229 # Debug port
