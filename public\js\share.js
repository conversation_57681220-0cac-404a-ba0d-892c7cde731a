/**
 * Share manager class
 */
class ShareManager {
  constructor(app = null) {
    this.app = app;

    this.initialize();
  }

  /**
   * Initialize share manager
   */
  initialize() {
    this.setupEventListeners();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    const elements = getShareElements();
    Utils.Events.on(elements.shareBtn, 'click', this.shareSession.bind(this));
    Utils.Events.on(elements.copyBtn, 'click', this.copyToClipboard.bind(this));
  }

  /**
   * Copy session URL to clipboard
   */
  async copyToClipboard() {
    const sessionId = this.app.sessions?.getCurrentSessionId();
    if (!sessionId) return;

    const url = this.generateSessionUrl(sessionId);

    try {
      await Utils.copyToClipboard(url);
      this.showShareSuccess();
    } catch (error) {
      console.warn('Failed to copy to clipboard:', error);
    }
  }

  /**
   * Share current session
   */
  async shareSession() {
    const sessionId = this.app.sessions?.getCurrentSessionId();
    if (!sessionId) {
      alert('No active session to share');
      return;
    }

    const sessionUrl = this.generateSessionUrl(sessionId);

    try {
      await Utils.copyToClipboard(sessionUrl);
      this.showShareSuccess();
    } catch (error) {
      console.warn('Failed to copy to clipboard:', error);
      this.showShareModal(sessionUrl);
    }
  }

  /**
   * Show success message for share
   */
  showShareSuccess() {
    Utils.showNotification('Session URL copied to clipboard!', 'success', 'share-notification');
  }

  /**
   * Show share modal with QR code
   * @param {string} url - URL to share
   */
  showShareModal(url) {
    const elements = getShareElements();
    elements.shareUrlInput.value = url;

    this.generateQrCode(url);

    Utils.DOM.showModal('share-modal');

    setTimeout(() => {
      elements.shareUrlInput.select();
      elements.shareUrlInput.setSelectionRange(0, 99999);
    }, 100);
  }

  /**
   * Generate session URL
   * @param {string} sessionId - Session ID
   * @returns {string} Full session URL
   */
  generateSessionUrl(sessionId) {
    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    return `${baseUrl}/${sessionId}`;
  }

  /**
   * Generate QR code for the session URL
   * @param {string} url - URL to encode in QR code
   */
  generateQrCode(url) {
    const elements = getShareElements();
    elements.qrContainer.innerHTML = '';

    try {
      // Generate QR code using QrCreator library
      QrCreator.render(
        {
          text: url,
          radius: 0, // Rounded corners
          ecLevel: 'L', // Error correction level
          fill: '#000000', // Black QR code
          background: '#ffffff', // White background
          size: 200, // 200x200 pixels
        },
        elements.qrContainer
      );
    } catch (error) {
      console.error('Failed to generate QR code:', error);
      // Show fallback text if QR generation fails
      elements.qrContainer.innerHTML = '<p style="text-align: center; padding: 20px;">QR code generation failed</p>';
    }
  }
}

/**
 * Retrieve all share-related DOM elements
 * @returns {Object} Share DOM elements
 */
const getShareElements = () => ({
  shareBtn: Utils.DOM.getId('share-btn'),
  copyBtn: Utils.DOM.getId('share-copy-btn'),
  shareUrlInput: Utils.DOM.getId('share-url'),
  qrContainer: Utils.DOM.getId('share-qr-code'),
});

const initializeShareManager = () => {
  Utils.Events.dispatch(document, 'shareManagerReady');
};

if (!window.Utils) {
  document.addEventListener('utilsReady', initializeShareManager);
} else {
  initializeShareManager();
}

window.ShareManager = ShareManager;
